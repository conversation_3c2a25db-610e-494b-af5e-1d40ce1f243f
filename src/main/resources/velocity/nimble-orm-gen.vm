#set($page_title='ORM代码生成器')

<div id="app" v-cloak>
    <el-form :model="form">
        <el-form-item>
            <el-input v-model="form.createTable" type="textarea" :rows="10" placeholder="CREATE TABLE ... / SELECT ... ">
            </el-input>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="genCode">生成Java代码</el-button>
            <span>开启BaseDO:</span><el-switch v-model="form.enableBaseDO"></el-switch>
            <el-tooltip class="item" effect="dark" placement="bottom">
                <div slot="content">基础DO类包含以下4个字段<br/>
                    1. id 自增，主键<br/>
                    2. deleted 软删除，0未删除，1已删除，类型tinyint<br/>
                    3. create_time 创建时间，DateTime类型<br/>
                    4. update_time 更新时间，DateTime类型<br/>
                    <br/>
                    您可以修改基础DO类的类名，它将保存在cookie中以便下次直接打开页面可以使用。
                </div>
                <i class="el-icon-question"></i>
            </el-tooltip>
            <span v-show="form.enableBaseDO">
                <el-input style="width:300px" v-model="form.baseDOClass" placeholder="baseDO类全称"></el-input>
            </span>
            <span style="float: right">
                <el-link type="primary" href="https://github.com/pugwoo/nimble-orm/issues" target="_blank">反馈问题</el-link>
            </span>
        </el-form-item>
    </el-form>
    <el-alert :title="resp.dupMessage" type="error" v-show="resp.dupMessage" :closable="false"></el-alert>
    <el-tabs type="border-card">
        <el-tab-pane label="DO类">
            <el-button v-clipboard:copy="resp.doClass">复制</el-button>
            <el-tooltip class="item" effect="dark" placement="bottom">
                <div slot="content">复制代码后，可以在IDEA中选中某个package直接Ctrl+V粘贴</div>
                <i class="el-icon-question"></i>
            </el-tooltip>
            <el-card class="box-card">
                <pre v-text="resp.doClass"></pre>
            </el-card>
        </el-tab-pane>
        #if($isLogin)
        <el-tab-pane label="IService">
            <el-button v-clipboard:copy="resp.iService">复制</el-button>
            <el-card class="box-card">
                <pre v-text="resp.iService"></pre>
            </el-card>
        </el-tab-pane>
        <el-tab-pane label="ServiceImpl">
            <el-button v-clipboard:copy="resp.serviceImpl">复制</el-button>
            <el-card class="box-card">
                <pre v-text="resp.serviceImpl"></pre>
            </el-card>
        </el-tab-pane>
        <el-tab-pane label="Controller">
            <el-button v-clipboard:copy="resp.controller">复制</el-button>
            <el-card class="box-card">
                <pre v-text="resp.controller"></pre>
            </el-card>
        </el-tab-pane>
        <el-tab-pane label="Controller(简)">
            <el-button v-clipboard:copy="resp.controllerSimplify">复制</el-button>
            <el-card class="box-card">
                <pre v-text="resp.controllerSimplify"></pre>
            </el-card>
        </el-tab-pane>
        <el-tab-pane label="Controller(无vm)">
            <el-button v-clipboard:copy="resp.controllerVue">复制</el-button>
            <el-card class="box-card">
                <pre v-text="resp.controllerVue"></pre>
            </el-card>
        </el-tab-pane>
        <el-tab-pane label="Velocity">
            <el-button v-clipboard:copy="resp.vm">复制</el-button>
            <el-card class="box-card">
                <pre v-text="resp.vm"></pre>
            </el-card>
        </el-tab-pane>
        <el-tab-pane label="Velocity(Vue组件)">
            <el-button v-clipboard:copy="resp.vmVueComponent">复制</el-button>
            <el-card class="box-card">
                <pre v-text="resp.vmVueComponent"></pre>
            </el-card>
        </el-tab-pane>
        #end
        <el-tab-pane label="ClickHouse(列)">
            <el-button v-clipboard:copy="resp.clickhouse">复制</el-button>
            <el-card class="box-card">
                <pre v-text="resp.clickhouse"></pre>
            </el-card>
        </el-tab-pane>
    </el-tabs>

</div>

<script src="${_contextPath_}/js/vue-clipboard.min.js"></script>
<script>
var vm = new Vue({
	el: '#app',
	data: {
		form:{
			createTable: "",
			enableBaseDO: true,
			baseDOClass: "$baseDOName"
		},
        resp: {}
	},
    created: function() {
        var enableBaseDO = localStorage.getItem("nimble-orm-gen.enableBaseDO")
        if (enableBaseDO) {
            this.form.enableBaseDO = enableBaseDO === "true"
        }
        var baseDOClass = localStorage.getItem("nimble-orm-gen.baseDOClass")
        if (baseDOClass) {
            this.form.baseDOClass = baseDOClass
        }
    },
    watch: {
        'form.enableBaseDO': function(newVal) {
            localStorage.setItem("nimble-orm-gen.enableBaseDO", newVal)
        },
        'form.baseDOClass': function(newVal) {
            localStorage.setItem("nimble-orm-gen.baseDOClass", newVal)
        }
    },
	methods: {
		genCode: function(){
			var that = this;
			Resource.post("${_contextPath_}/nimble-orm-gen.json", that.form, function(resp){
                that.resp = resp.data
			}, function(resp) {
                Message.alert(resp.msg)
            })
		}
	}
})
</script>