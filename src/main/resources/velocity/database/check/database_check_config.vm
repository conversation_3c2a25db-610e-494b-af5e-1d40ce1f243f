<!-- database_check_config.vm -->

#set($page_title='检查sql配置')

#parse("database/check/database_check_item.vm")

<style>
  .database-check-config-table-expand label {width: 120px;color: #99a9bf;}
  .database-check-config-table-expand .el-form-item {width: 100%;}
</style>

<div id="app" v-cloak>
  <el-form :inline="true" @keyup.native.enter="getData">
    <el-form-item>
      <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
      <el-button @click="getData">刷新</el-button>
      <el-button @click="showBuildInVariable=true">内置变量</el-button>
    </el-form-item>
  </el-form>

  <el-table :data="tableData" border stripe v-loading.body="tableLoading">
    <el-table-column type="expand">
      <template slot-scope="props">
        <el-form :data="props" class="database-check-config-table-expand">
          <el-form-item label="检查sql">{{props.row.sql}}</el-form-item>
          <el-form-item label="断言表达式">{{props.row.assertion}}</el-form-item>
          <el-form-item label="检查明细sql">{{props.row.detailSql}}</el-form-item>
          <el-form-item label="成功时记录频率">{{props.row.succLogRateSecs}}秒/条</el-form-item>
          <el-form-item label="备注">{{props.row.comment}}</el-form-item>
          <el-form-item label="数据库ID">{{props.row.databaseId}}</el-form-item>
          <el-form-item label="异常通知邮箱">{{props.row.sendEmail}}</el-form-item>
        </el-form>
      </template>
    </el-table-column>
    <el-table-column prop="id" label="id" width="50"></el-table-column>
    <el-table-column prop="name" label="检查名称" min-width="140"></el-table-column>
    <el-table-column label="数据库" width="120">
      <template slot-scope="props">
        <span>{{props.row.database}}</span>
      </template>
    </el-table-column>
    <el-table-column prop="databaseName" label="数据库名"></el-table-column>
    <el-table-column prop="category" label="分类"></el-table-column>
    <el-table-column prop="rateSeconds" label="检查频率(秒)"></el-table-column>
    <el-table-column prop="cronExpression">
      <template slot="header">
        <span>cron表达式</span>
      </template>
    </el-table-column>
    <el-table-column label="最后执行">
      <template slot-scope="props">
        <span v-show="props.row.isLastSuccess===true" style="color: green">成功</span>
        <span v-show="props.row.isLastSuccess===false" style="color: red">失败</span>
        <span v-show="props.row.isLastSuccess===null">未执行</span>
      </template>
    </el-table-column>
    <el-table-column label="最后执行时间">
      <template slot-scope="props">
        <el-tooltip class="item" effect="dark" :content="props.row.lastTime" placement="bottom">
          <span>{{props.row.lastTimeStr}}</span>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column label="最后错误时间">
      <template slot-scope="props">
        <el-tooltip class="item" effect="dark" :content="props.row.lastErrorTime" placement="bottom">
          <span>{{props.row.lastErrorTimeStr}}</span>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column prop="countSuccess" label="正常次数" width="60"></el-table-column>
    <el-table-column prop="countError" label="异常次数" width="60"></el-table-column>
    <el-table-column label="启用禁用" width="65">
      <template slot-scope="props">
        <el-switch v-model="props.row.isEnabled" active-color="#13ce66" inactive-color="#BEBEBE"
                   @change="enableCheck($event, props.row.id)">
        </el-switch>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="150px">
      <template slot-scope="scope">
        <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
        <el-button type="primary" size="small" @click="getResult(scope.row)">结果</el-button>
      </template>
    </el-table-column>
  </el-table>

  <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                 :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
  </el-pagination>

  <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false">
    <el-alert v-show="addEditForm.isBuildIn" title="请注意：修改内置监控的内容会在数据库开启整体监控时被覆盖。" type="warning">
    </el-alert>
    <el-form :model="addEditForm" label-position="right" label-width="120px" :rules="rules" ref="addEditForm">
      <el-form-item label="* 检查名称" prop="name">
        <el-input v-model="addEditForm.name" placeholder="检查名称"></el-input>
      </el-form-item>
      <el-form-item label="* 数据库配置id" prop="databaseId">
        <el-select v-model="addEditForm.databaseId" placeholder="请选择">
          <el-option v-for="item in databases" :key="item.databaseId" :label="item.name" :value="item.databaseId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据库名" prop="databaseName">
        <el-input v-model="addEditForm.databaseName" placeholder="数据库名,允许为空"></el-input>
      </el-form-item>
      <el-form-item label="* 分类" prop="category">
        <el-input v-model="addEditForm.category" placeholder="分类 只是一个标识"></el-input>
      </el-form-item>
      <el-form-item label="* 检查频率(秒)" prop="rateSeconds">
        <el-input v-model.number="addEditForm.rateSeconds" placeholder="检查频率(秒)"></el-input>
      </el-form-item>
      <el-form-item label="* cron表达式" prop="cronExpression">
        <el-input v-model="addEditForm.cronExpression" placeholder="spring的cron表达式，精确到秒"></el-input>
      </el-form-item>
      <el-form-item label="成功时记录频率" prop="succLogRateSecs">
        <el-input v-model="addEditForm.succLogRateSecs" placeholder="秒/条"></el-input>
      </el-form-item>
      <el-form-item label="* 检查sql" prop="sql">
        <el-input type="textarea" v-model="addEditForm.sql" placeholder="检查sql" :rows="6"></el-input>
      </el-form-item>
      <el-form-item label="* 断言表达式" prop="assertion">
        <el-input type="textarea" v-model="addEditForm.assertion" placeholder="检查断言表达式: 例: rows[0].count > 0" :rows="1"></el-input>
      </el-form-item>
      <el-form-item label="* 检查明细sql" prop="detailSql">
        <el-input type="textarea" v-model="addEditForm.detailSql" placeholder="检查明细sql，当断言为false时记录" :rows="4"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="comment">
        <el-input v-model="addEditForm.comment" placeholder="备注"></el-input>
      </el-form-item>
      <el-form-item label="异常通知邮箱" prop="sendEmail">
        <el-input v-model="addEditForm.sendEmail" placeholder="异常通知邮箱，多个分号隔开" :style="{width: '435px'}"></el-input>
        <el-button @click="testSend">测试发送</el-button>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button type="success" @click="tryCheck">测试断言</el-button>
      <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
      <el-button @click="showDialog = false">取消</el-button>
      <el-button type="primary" @click="doAddOrEdit">确定</el-button>
    </div>
  </el-dialog>

  <!-- 加v-if让对话框销毁，这样里面内容下次打开时可以刷新-->
  <el-dialog title="监控结果" :visible.sync="showResult" top="10px" width="1360" v-if="showResult">
    <database-check-item :database-check-config-id="showResultCheckId"></database-check-item>
  </el-dialog>

  <el-dialog title="内置变量" :visible.sync="showBuildInVariable" top="10px" width="600">
    <el-table :data="buildInVariable">
      <el-table-column prop="name" label="变量名称"></el-table-column>
      <el-table-column prop="comment" label="说明"></el-table-column>
    </el-table>
  </el-dialog>
</div>

<script>
  var defaultQueryForm = {page: 1, pageSize: 10}
  var defaultAddForm = {isEnabled: true}
  var vm = new Vue({
    el: '#app',
    data: {
      queryForm: Utils.copy(defaultQueryForm),
      addEditForm: Utils.copy(defaultAddForm),
      rules: {
        name: Form.notBlankValidator('检查名称不能为空'),
        databaseId: {
          validator: function (rule, value, callback) {
            if (!(value && (value + '').trim())) {
              callback(new Error('数据库配置id不能为空'));
            } else if (!Number.isInteger(value)) {
              callback(new Error('数据库配置id必须为数字'));
            } else {
              callback()
            }
          },
          trigger: 'blur',
        },
        category: Form.notBlankValidator('分类不能为空'),
        sql: Form.notBlankValidator('检查sql不能为空'),
        assertion: Form.notBlankValidator('断言表达式不能为空'),
      },
      total: 0, tableData: [], tableLoading: false,
      showDialog: false, dialogTitle: '',
      databases: [], // 数据库下拉选择
      showResult:false, showResultCheckId: null,
      showBuildInVariable: false,
      buildInVariable: [{name: "${TODAY}", comment: "今天日期yyyy-MM-dd"},
        {name: "${YESTERDAY}", comment: "昨天日期yyyy-MM-dd"}]
    },
    created: function() {
      var that = this
      this.getData()
      Resource.get("${_contextPath_}/database/get_database_for_select", {}, function(resp){
        that.databases = resp.data
      })
    },
    methods: {
      getData: function() {
        var that = this
        that.tableLoading = true
        Resource.get("${_contextPath_}/database_check_config/get_page", this.queryForm, function(resp){
          that.tableData = resp.data.data
          that.total = resp.data.total
          that.tableLoading = false
        })
      },
      pageChange: function(page) {
        this.queryForm.page = page
        this.getData()
      },
      resetQuery: function() {
        this.queryForm = Utils.copy(defaultQueryForm)
      },
      handleDelete: function(row) {
        var that = this
        Message.confirm("确定要删除吗?", function(){
          Resource.post("${_contextPath_}/database_check_config/delete", {id: row.id}, function(){
            that.showDialog = false
            Message.success("删除成功，列表已刷新")
            that.getData()
          })
        })
      },
      handleAddOrEdit: function(isAdd, row) {
        this.showDialog = true
        this.dialogTitle = isAdd ? '新增检查sql配置表' : '编辑'
        Form.clearError(this, 'addEditForm')
        this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
      },
      doAddOrEdit: function() {
        var that = this
        var isEdit =  this.addEditForm.id
        Form.validate(this, 'addEditForm', function() {
          Resource.post("${_contextPath_}/database_check_config/add_or_update", that.addEditForm, function(resp){
            Message.success(isEdit ? "修改成功" : "新增成功")
            isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
            that.getData()
          })
        })
      },
      tryCheck: function() {
        Resource.post("${_contextPath_}/database_check_config/try_check", this.addEditForm, function(resp) {
          if (resp.data.isSuccess) {Message.success("断言成功")}
          else {
            Message.confirm("断言失败, 信息:" + resp.data.errorMsg + ", 数据库数据:" + resp.data.sqlRowsJson)
          }
        })
      },
      enableCheck: function($event, configId) {
        var that = this
        Resource.post("${_contextPath_}/database_check_config/enable_disable_config", {
          configId: configId, enabled: $event
        }, function(resp){
          Message.success("修改成功")
          that.getData()
        })
      },
      getResult: function(row) {
        this.showResultCheckId = row.id
        this.showResult = true
      },
      testSend: function() {
        var that = this
        Resource.post("${_contextPath_}/database_check_config/test_send_email", {
          sendEmail: that.addEditForm.sendEmail
        }, function(resp){
          Message.success("发送成功，将发送：hello+当前时间")
        })
      }
    }
  })
</script>
