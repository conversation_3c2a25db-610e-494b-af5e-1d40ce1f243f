#set($page_title='数据Group探索')

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
        <el-form-item label="* 数据库实例">
            <el-select v-model="queryForm.databaseId" placeholder="请选择，必填">
                <el-option v-for="item in databases" :key="item.databaseId" :label="item.name" :value="item.databaseId">
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="数据库名称">
            <el-select v-model="queryForm.databaseName" placeholder="可选">
                <el-option v-for="item in databaseNames" :key="item" :label="item" :value="item">
                </el-option>
            </el-select>
        </el-form-item>
        <br/>
        <el-form-item label="查询SQL">
            <el-input type="textarea" v-model="queryForm.querySql" :rows="4" style="width: 900px"></el-input>
        </el-form-item>
        <el-form-item>
            <el-form-item label="聚合函数">
                <el-input v-model="queryForm.aggFunc"></el-input>
            </el-form-item>
            <br/>
            <el-button type="primary" @click="query">查询</el-button>
        </el-form-item>
    </el-form>

    <el-alert :title="'总条数:' + result.totalRows" type="success" v-show="result.totalRows"></el-alert>
    <el-table :data="result.groupDataList" border stripe v-loading.body="tableLoading" v-sticky-header>
        <el-table-column label="列名" prop="columnName" width="160px"></el-table-column>
        <el-table-column label="注释" prop="columnComment" width="160px"></el-table-column>
        <el-table-column label="值个数" prop="distinctValueCount" width="100px"></el-table-column>
        <el-table-column label="最大值" prop="maxValue" width="120px"></el-table-column>
        <el-table-column label="最小值" prop="minValue" width="120px"></el-table-column>
        <el-table-column label="top值" min-width="180px">
            <template slot-scope="props">
                <div v-for="o in props.row.topValueAndCount">
                    {{'值:' + o.value + ", " + numberLabel + ":" + o.count }}
                </div>
                <el-button type="plain" size="small" @click="showTopDetail(props.row)" v-if="props.row.distinctValueCount > 40">查看{{props.row.distinctValueCount > 1000 ? ('前' + 1000) : ('全部' + props.row.distinctValueCount)}}个</el-button>
            </template>
        </el-table-column>
        <el-table-column label="bottom值" min-width="180px">
            <template slot-scope="props">
                <div v-for="o in props.row.bottomValueAndCount">
                    {{'值:' + o.value + ", " + numberLabel + ":" + o.count }}
                </div>
            </template>
        </el-table-column>
    </el-table>

    <el-dialog title="" :visible.sync="topDetailShow" top="10px" v-loading.body="topDetailTableLoading" width="1200">
        <div>
            <div>搜索值：<span><el-input v-model="searchValue" placeholder="输入关键字搜索" style="width: 600px;"/></span></div>
            <el-table :data="topDetailData" border stripe v-loading.body="topDetailTableLoading" v-sticky-header>
                <el-table-column prop="value" label="值" sortable width="1000"></el-table-column>
                <el-table-column prop="count" :label="numberLabel" sortable></el-table-column>
            </el-table>
        </div>
    </el-dialog>
</div>

<script>
var app = new Vue({
    el: '#app',
    data: {
        queryForm:{
            aggFunc: "count(*)"
        },
        databases: [], // 数据库下拉选择
        databaseNames: [],
        result: {},
        tableLoading: false,
        topDetailShow: false,
        topDetailData: [],
        originTopDetailData: [],
        topDetailTableLoading: false,
        searchValue: ""
    },
    computed: {
        numberLabel: function() {
            return this.queryForm.aggFunc === "count(*)" ? "个数" : "数值";
        }
    },
    created: function() {
        var that = this
        Resource.get("${_contextPath_}/database/get_database_for_select", {}, function(resp){
            that.databases = resp.data
        })
    },
    watch: {
        "queryForm.databaseId": function(val) {
            var that = this
            Resource.get("${_contextPath_}/database/get_database_name_for_select", {databaseId: val}, function(resp){
                that.databaseNames = resp.data
            })
        },
        "searchValue": function(val) {
            if (val) {
                this.topDetailData = this.originTopDetailData.filter(function (item) {
                    return String(item.value).indexOf(val) > -1
                })
            } else {
                this.topDetailData = this.originTopDetailData
            }
        }
    },
    methods: {
        query: function () {
            var that = this
            that.tableLoading = true
            Resource.postJson("${_contextPath_}/database_view_group_data/view_group_data", that.queryForm, function(resp) {
                that.result = resp.data
                that.tableLoading = false
            }, function(resp) {
                that.tableLoading = false
                Message.error(resp.msg || resp.message)
            })
        },
        showTopDetail: function(row) {
            var that = this
            that.searchValue = ""
            that.topDetailShow = true
            that.topDetailData = []
            that.topDetailTableLoading = true
            that.queryForm.specificColumn = row.columnName
            Resource.postJson("${_contextPath_}/database_view_group_data/view_group_data_for_specific_column", that.queryForm, function(resp) {
                that.topDetailData = resp.data.groupDataList[0].topValueAndCount
                that.originTopDetailData = that.topDetailData
                that.topDetailTableLoading = false
            }, function(resp) {
                that.topDetailTableLoading = false
                Message.error(resp.msg || resp.message)
            })
        }
    }
})
</script>
