<style>
</style>

<script type="text/x-template" id="databaseExecuteResult">

    <div>
        <el-form :inline="true" @keyup.native.enter="getData">
            <el-form-item label="id">
                <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
            </el-form-item>
            <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
            <el-form-item>
                <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
                <el-button @click="resetQuery">重置</el-button>
                <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
            </el-form-item>
        </el-form>

        <el-table :data="tableData" border stripe v-loading.body="tableLoading">
            <el-table-column prop="id" label="id"></el-table-column>
            <el-table-column prop="name" label="name"></el-table-column>
##            <el-table-column prop="taskId" label="taskId"></el-table-column>
            <el-table-column prop="status" label="status"></el-table-column>
            <el-table-column prop="sql" label="sql"></el-table-column>
            <el-table-column prop="affectedRows" label="affectedRows"></el-table-column>
            <el-table-column prop="currentKeyValue" label="currentKeyValue"></el-table-column>
            <el-table-column prop="errorMessage" label="errorMessage"></el-table-column>
            <el-table-column prop="executionTime" label="executionTime"></el-table-column>
            <el-table-column prop="createTime" label="createTime"></el-table-column>
##            <el-table-column label="操作">
##                <template slot-scope="scope">
##                    <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
##                </template>
##            </el-table-column>
        </el-table>

        <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                       :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
        </el-pagination>

        <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false" :append-to-body='true'> <!-- append-to-body修复弹框蒙版问题 -->
            <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
                <el-form-item label="name" prop="name">
                    <el-input v-model="addEditForm.name" placeholder="执行任务名称，冗余字段"></el-input>
                </el-form-item>
                <el-form-item label="taskId" prop="taskId">
                    <el-input v-model="addEditForm.taskId" placeholder="关联database_execute_task的id" :disabled="!!taskId"></el-input>
                </el-form-item>
                <el-form-item label="status" prop="status">
                    <el-input v-model="addEditForm.status" placeholder="执行结果状态，FAILED失败，SUCCESS成功"></el-input>
                </el-form-item>
                <el-form-item label="sql" prop="sql">
                    <el-input v-model="addEditForm.sql" placeholder="实际执行的SQL"></el-input>
                </el-form-item>
                <el-form-item label="affectedRows" prop="affectedRows">
                    <el-input v-model="addEditForm.affectedRows" placeholder="实际修改的行数"></el-input>
                </el-form-item>
                <el-form-item label="currentKeyValue" prop="currentKeyValue">
                    <el-input v-model="addEditForm.currentKeyValue" placeholder="当前执行的主键值，取起始值"></el-input>
                </el-form-item>
                <el-form-item label="errorMessage" prop="errorMessage">
                    <el-input v-model="addEditForm.errorMessage" placeholder="错误信息"></el-input>
                </el-form-item>
                <el-form-item label="executionTime" prop="executionTime">
                    <el-input v-model="addEditForm.executionTime" placeholder="执行时间（毫秒）"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
                <el-button @click="showDialog = false">取消</el-button>
                <el-button type="primary" @click="doAddOrEdit">确定</el-button>
            </div>
        </el-dialog>

    </div>

</script>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    Vue.component('database-execute-result', {
        template: '#databaseExecuteResult',
        data: function () {
            return {
                queryForm: Utils.copy(defaultQueryForm),
                addEditForm: Utils.copy(defaultAddForm),
                rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
                total: 0, tableData: [], tableLoading: false,
                showDialog: false, dialogTitle: ''
            }
        },
        props: {
            taskId: {
                type: [Number, String],
                default: null
            }
        },
        watch: {
            'taskId': {
                immediate: true,
                handler: function() {
                    if (this.taskId) {
                        this.queryForm.page = 1;
                        this.getData();
                    }
                }
            }
        },
        created: function() {
            // Don't load data here, let the watcher handle it
        },
        methods: {
            getData: function() {
                var that = this;
                that.tableLoading = true;
                var params = Utils.copy(this.queryForm);
                if (this.taskId) {
                    params.taskId = this.taskId;
                }
                Resource.get("${_contextPath_}/database_execute_result/get_page", params, function(resp){
                    that.tableData = resp.data.data;
                    that.total = resp.data.total;
                    that.tableLoading = false;
                });
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm);
                if (this.taskId) {
                    this.queryForm.taskId = this.taskId;
                }
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/database_execute_result/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true;
                this.dialogTitle = isAdd ? '新增' : '编辑';
                Form.clearError(this, 'addEditForm');
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row);
                if (isAdd && this.taskId) {
                    this.addEditForm.taskId = this.taskId;
                }
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/database_execute_result/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            }
        }
    })
</script>