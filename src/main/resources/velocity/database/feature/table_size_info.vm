<script type="text/x-template" id="tableSizeInfo">
    <div>
        <p>
            <el-form :inline="true" @keyup.native.enter="filterData">
                <el-form-item label="数据库名称">
                    <el-input v-model="filter.databaseName" placeholder="数据库名称"></el-input>
                </el-form-item>
                <el-form-item label="表名">
                    <el-input v-model="filter.tableName" placeholder="表名"></el-input>
                </el-form-item>
                <span v-show="clusterNodeCount>0">集群节点数：{{clusterNodeCount}}</span>
                <span v-show="shardNum"> 分片数：【{{shardNum}}】</span>
                <span v-show="databaseType=='ClickHouse'" style="display: inline-block;width: 440px;color: gray;">
                    对于Distributed表，以下数据(磁盘大小/数据大小/行数)为单个切片量，实际量需乘以集群分片数。若有多个集群，请自行确认所属集群。
                </span>
            </el-form>
        </p>
        <el-table :data="tableData" border stripe v-loading.body="tableLoading" v-sticky-header>
            <el-table-column prop="database" label="数据库名称"></el-table-column>
            <el-table-column prop="table" label="表名" min-width="160"></el-table-column>
            <el-table-column prop="engine" label="引擎"></el-table-column>
            <el-table-column prop="diskSizeByte" label="真实占用磁盘大小" :formatter="bytesToSize" sortable></el-table-column>
            <el-table-column prop="logicSizeByte" label="逻辑上数据大小" :formatter="bytesToSize" sortable></el-table-column>
            <el-table-column prop="compressPercent" label="压缩百分比" :formatter="percent" sortable></el-table-column>
            <el-table-column prop="rows" label="行数" :formatter="numberFormat" sortable></el-table-column>
        </el-table>
    </div>
</script>

<script>
Vue.component('table-size-info', {
    template: '#tableSizeInfo',
    data: function () {
        return {
            originTableData: [], // 这个是保留原始数据，用于filter
            tableData: [],
            tableLoading: false,
            databaseType: "",
            clusterNodeCount: 0,
            shardNum: 0,
            filter: { /*纯前端过滤*/
                databaseName: "",
                tableName: ""
            }
        }
    },
    props: {
        databaseId: Number
    },
    created: function() {
        this.getData()
    },
    watch: {
        "filter.databaseName": function() {
            this.filterData()
        },
        "filter.tableName": function() {
            this.filterData()
        }
    },
    methods: {
        getData: function () {
            var that = this
            that.tableLoading = true
            Resource.get("${_contextPath_}/database_feature/get_table_size_info", {
                databaseId: that.databaseId
            }, function(resp){
                that.tableData = resp.data.tableSizeInfo
                that.databaseType = resp.data.databaseType
                that.clusterNodeCount = resp.data.clusterNodeCount
                that.shardNum = resp.data.shardNum
                that.tableLoading = false
                that.originTableData = Utils.copy(that.tableData) // 这里需要copy，因为增加总计时修改了

                that.addTotalRow(that.tableData)
            })
        },
        addTotalRow: function(tableData) {
            // 增加一列总计
            var total = {
                database: "总计",
                table: "",
                diskSizeByte: 0,
                logicSizeByte: 0,
                compressPercent: "-",
                rows: 0
            }
            tableData.forEach(function (item) {
                total.diskSizeByte += item.diskSizeByte
                total.logicSizeByte += item.logicSizeByte
                total.rows += item.rows
            })
            total.compressPercent = total.logicSizeByte === 0 ? "-" : (total.diskSizeByte / total.logicSizeByte * 100).toFixed(2)
            tableData.unshift(total)
        },
        bytesToSize: function(row, column, cellValue, index) {
            var bytes = cellValue
            if (bytes === 0) return '0 B';
            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        numberFormat: function (row, column, cellValue, index) {
            var value = cellValue.toString();
            var parts = value.split('.');
            parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            return parts.join('.');
        },
        percent: function (row, column, cellValue, index) {
            return cellValue + '%'
        },
        filterData: function() {
            var that = this
            that.tableData = that.originTableData.filter(function (item) {
                return (!that.filter.databaseName || item.database.indexOf(that.filter.databaseName) > -1)
                        && (!that.filter.tableName || item.table.indexOf(that.filter.tableName) > -1)
            })
            this.addTotalRow(that.tableData)
        }
    }
})
</script>