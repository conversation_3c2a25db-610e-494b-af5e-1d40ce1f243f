
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.admin.bean.ResultBean;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

@RestController
@RequestMapping(value = "/${table.allLowClassName}")
public class ${table.className}Controller {

    @Autowired
    private ${table.className}Service ${table.lowClassName}Service;
    
    @GetMapping("list")
    public ModelAndView list() {
        return new ModelAndView("${table.allLowClassName}");
    }

    @GetMapping("get_page")
    public WebJsonBean<Map<String, Object>> getPage(int page, int pageSize) {
        PageData<${table.className}DO> pageData = ${table.lowClassName}Service.getPage(page, pageSize);
        
        Map<String, Object> result = PageUtils.trans(pageData, o -> {
            Map<String,Object> map = new HashMap<>();
#foreach($col in $columns)
#if(!$table.useBaseDO || $table.useBaseDO && !($col.name=='deleted'))
            map.put("$col.javaVarName", o.get${col.upJavaVarName}());
#end
#end
            return map;
        });
        
        return WebJsonBean.ok(result);
    }
    
    @PostMapping("add_or_update")
    public WebJsonBean<Long> addOrUpdate(
#foreach($col in $columns)
#if(!$table.useBaseDO || $table.useBaseDO && !($col.name=='deleted'||$col.name=='create_time'||$col.name=='update_time'))
#if($foreach.count>1),
#end
                $col.javaType ${col.javaVarName}#end#end) {        
        
        // TODO check parameters
        
        ${table.className}DO ${table.lowClassName}DO = new ${table.className}DO();
#foreach($col in $columns)
#if(!$table.useBaseDO || $table.useBaseDO && !($col.name=='deleted'||$col.name=='create_time'||$col.name=='update_time'))
        ${table.lowClassName}DO.set${col.upJavaVarName}(${col.javaVarName});
#end
#end
        
        ResultBean<Long> result = ${table.lowClassName}Service.insertOrUpdate(${table.lowClassName}DO);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }
    
    @PostMapping("delete")
    public WebJsonBean<Boolean> delete(#foreach($col in $columns)#if($col.key)#if($foreach.count>1), #end$col.javaType ${col.javaVarName}#end#end) {
#foreach($col in $columns)
#if($col.key)
        WebCheckUtils.assertNotNull(${col.javaVarName}, "缺少参数${col.javaVarName}");
#end
#end
        return WebJsonBean.ok(${table.lowClassName}Service.deleteById(#foreach($col in $columns)#if($col.key)#if($foreach.count>1), #end${col.javaVarName}#end#end));
    }

}
