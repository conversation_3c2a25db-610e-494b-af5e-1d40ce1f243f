
import lombok.Data;
import lombok.ToString;

#foreach($import in $imports)
import $import;
#end

#if($table.comment)
/**
 * $table.comment
 */
#end
@Data
@ToString
#if($table.sqlType=='CREATE')@Table("$table.name")
#end
public class ${table.className}#if($table.sqlType=='CREATE')DO#end#if($table.sqlType=='SELECT')VO#end #if($table.useBaseDO)extends $table.baseClassSimpleName #end{

#foreach($col in $columns)
#if(!$table.useBaseDO || $table.useBaseDO && !$col.isBaseColumn)
#if($col.comment)    /** $col.comment<br/>Column: [$col.name] */
#end
    @Column(value = "$col.name"#if($col.key), isKey = true#end#if($col.autoIncrement), isAutoIncrement = true#end#if($col.javaType=='Boolean'&&($col.downJavaVarName=='deleted'||$col.downJavaVarName=='isDeleted'||$col.downJavaVarName=='isDelete')), softDelete = {"0", "1"}#end)
    private $col.javaType $col.downJavaVarName;

#end
#end
}