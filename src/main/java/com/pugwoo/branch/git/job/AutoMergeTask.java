package com.pugwoo.branch.git.job;

import com.pugwoo.branch.git.entity.GitBranchConfigDO;
import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.model.GitBranchInfoDTO;
import com.pugwoo.branch.git.service.IJGitService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.redis.exception.NotGetLockException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 负责自动合并master分支和自动合并远程分支的定时任务
 */
@Service
@Slf4j
public class AutoMergeTask {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;
    @Autowired
    private IJGitService jGitService;

    @Data
    public static class GitBranchConfigVO extends GitBranchConfigDO {
        @RelatedColumn(localColumn = "repo_id", remoteColumn = "id")
        private GitRepositoryDO gitRepositoryDO;
    }

    @Synchronized(throwExceptionIfNotGetLock = false) // 自动合并任务，没抢到锁就算了
    @Scheduled(fixedDelay = 60000 * 10, initialDelay = 30000) // 30秒是为何和merge master错开
    public void autoMergeRemote() throws Exception {
        List<GitBranchConfigVO> all = dbHelper.getAll(GitBranchConfigVO.class,
                "where is_auto_merge_remote=1 and remote_url!=''");
        Map<Long, List<GitBranchConfigVO>> map = ListUtils.toMapList(all, GitBranchConfigDO::getRepoId, o -> o);
        for (Map.Entry<Long, List<GitBranchConfigVO>> entry : map.entrySet()) {
            List<GitBranchConfigVO> gitBranchConfigDOS = entry.getValue();
            GitRepositoryDO gitRepositoryDO = gitBranchConfigDOS.getFirst().getGitRepositoryDO();
            if (gitRepositoryDO == null) {
                log.error("autoMergeRemote gitRepositoryDO is null, repoId: {}", entry.getKey());
                continue;
            }

            // 查询该分支的所有分支，这里还是走缓存好了，不然调用分支的频率太高了，等于是1分钟两次
            List<GitBranchInfoDTO> developList = jGitService.getDevelopListWithCommitId(gitRepositoryDO, true);
            Set<String> existBranches = ListUtils.toSet(developList, GitBranchInfoDTO::getName);

            for (GitBranchConfigVO gitBranchConfigDO : gitBranchConfigDOS) {
                String branchName = gitBranchConfigDO.getBranchName();
                if (existBranches.contains(branchName)) {
                    boolean isMergeFail;
                    String errorMsg = "";
                    try {
                        isMergeFail = !jGitService.developBranchMergeRemote(gitRepositoryDO, branchName, gitBranchConfigDO);
                    } catch (NotGetLockException e) {
                        // 忽略加锁失败，下一轮继续
                        continue;
                    } catch (Exception e) {
                        isMergeFail = true;
                        errorMsg = e.toString();
                    }
                    gitBranchConfigDO.setIsAutoMergeRemote(null); // 不修改开关，不然和页面有冲突
                    gitBranchConfigDO.setIsMergeRemoteFail(isMergeFail); // 只修改结果
                    gitBranchConfigDO.setMergeRemoteFailMsg(errorMsg);
                } else {
                    log.warn("auto merge remote repoId:{} exist branches:{}, expect branch:{}",
                            gitRepositoryDO.getId(), JSON.toJson(existBranches), branchName);
                    // 如果分支不存在，则当做成功处理，同时清理掉配置
                    gitBranchConfigDO.setIsAutoMergeRemote(false);
                    gitBranchConfigDO.setIsMergeRemoteFail(false);
                }
            }
            dbHelper.update(gitBranchConfigDOS); // 批量更新
        }
    }

    @Synchronized(throwExceptionIfNotGetLock = false) // 自动合并任务，没抢到锁就算了
    @Scheduled(fixedDelay = 60000)
    public void autoMergeMaster() throws Exception {
        List<GitBranchConfigVO> all = dbHelper.getAll(GitBranchConfigVO.class, "where is_auto_merge_master=1");
        Map<Long, List<GitBranchConfigVO>> map = ListUtils.toMapList(all, GitBranchConfigDO::getRepoId, o -> o);
        for (Map.Entry<Long, List<GitBranchConfigVO>> entry : map.entrySet()) {
            List<GitBranchConfigVO> gitBranchConfigDOS = entry.getValue();
            GitRepositoryDO gitRepositoryDO = gitBranchConfigDOS.getFirst().getGitRepositoryDO();
            if (gitRepositoryDO == null) {
                log.error("autoMergeMaster gitRepositoryDO is null, repoId: {}", entry.getKey());
                continue;
            }

            // 查询该分支的所有分支，这里还是走缓存好了，不然调用分支的频率太高了，等于是1分钟两次
            List<GitBranchInfoDTO> developList = jGitService.getDevelopListWithCommitId(gitRepositoryDO, true);
            Set<String> existBranches = ListUtils.toSet(developList, GitBranchInfoDTO::getName);

            for (GitBranchConfigVO gitBranchConfigDO : gitBranchConfigDOS) {
                String branchName = gitBranchConfigDO.getBranchName();
                if (existBranches.contains(branchName)) {
                    boolean isMergeFail;
                    String errorMsg = "";
                    try {
                        isMergeFail = !jGitService.developBranchMergeMaster(gitRepositoryDO, branchName);
                    } catch (NotGetLockException e) {
                        // 忽略加锁失败，下一轮继续
                        continue;
                    } catch (Exception e) {
                        isMergeFail = true;
                        errorMsg = e.toString();
                    }
                    gitBranchConfigDO.setIsAutoMergeMaster(null); // 不修改开关，不然和页面有冲突
                    gitBranchConfigDO.setIsMergeMasterFail(isMergeFail); // 只修改结果
                    gitBranchConfigDO.setMergeMasterFailMsg(errorMsg);
                } else {
                    // 如果分支不存在，则当做成功处理，同时清理掉配置
                    gitBranchConfigDO.setIsAutoMergeMaster(false);
                    gitBranchConfigDO.setIsMergeMasterFail(false);
                }
            }
            dbHelper.update(gitBranchConfigDOS); // 批量更新
        }
    }

}
