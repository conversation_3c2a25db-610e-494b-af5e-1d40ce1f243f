package com.pugwoo.branch.git.web;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.admin.web.interceptor.AdminUserLoginContext;
import com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor;
import com.pugwoo.bootwebext.JsonParam;
import com.pugwoo.branch.code_scan.enums.CheckRuleLevelEnum;
import com.pugwoo.branch.git.config.Constants;
import com.pugwoo.branch.git.entity.GitBranchConfigDO;
import com.pugwoo.branch.git.entity.GitIngressBranchDO;
import com.pugwoo.branch.git.entity.GitIngressDO;
import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.enums.IngressTypeEnum;
import com.pugwoo.branch.git.model.ConflictResultDTO;
import com.pugwoo.branch.git.model.GitBranchInfoDTO;
import com.pugwoo.branch.git.service.*;
import com.pugwoo.branch.git.utils.VersionUtils;
import com.pugwoo.branch.git.vo.GitIngressVO;
import com.pugwoo.branch.git.vo.GitRepo4BuildStatusVO;
import com.pugwoo.branch.git.vo.GitRepositoryVO;
import com.pugwoo.branch.git.vo.GitTagsVO;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.cache.HiSpeedCacheContext;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Permission(value = "ReadGit", name = "Git查看列表")
@RestController
@RequestMapping("/git_repository")
public class GitRepositoryController {

    @Autowired
    private IGitRepositoryService repositoryService;
    @Autowired
    private IJGitService jGitService;
    @Autowired
    private IGitIngressService gitIngressService;
    @Autowired
    private IGitIngressBranchService gitIngressBranchService;
    @Autowired
    private IGitCloneService gitCloneService;
    @Autowired
    private IGitPermissionService gitPermissionService;
    @Autowired
    private ILockReleaseService lockReleaseService;

    private static final ThreadPoolExecutor fetchGitThreadPool = ThreadPoolUtils.createThreadPool(50,
            1000, 50, "fetchGitThreadPool");

    @RequestMapping("get_build_status")
    public WebJsonBean<?> getBuildStatus(int page, int pageSize, String name) {
        PageData<GitRepo4BuildStatusVO> buildStatus = repositoryService.getBuildStatus(page, pageSize, name);
        Map<String, Object> data = PageUtils.trans(buildStatus, o -> {
            Map<String, Object> result = new HashMap<>();
            result.put("id", o.getId());
            result.put("developBuildStatus", o.getLastDevelopBuildStatus());
            // tag去掉release-前缀，和get_page保持一致
            Map<String, String> tagBuildStatus = new HashMap<>();
            if(o.getTagBuildStatus() != null) {
                for(Map.Entry<String, String> entry : o.getTagBuildStatus().entrySet()) {
                    String key = entry.getKey();
                    if(key != null && key.startsWith(Constants.RELEASE)) {
                        key = key.substring(8);
                    }
                    tagBuildStatus.put(key, entry.getValue());
                }
            }
            result.put("tagBuildStatus", tagBuildStatus);
            return result;
        });
        return WebJsonBean.ok(data);
    }

    @RequestMapping("get_page")
    public WebJsonBean getPage(int page, int pageSize, String name, String branchName, final boolean forceUpdate,
                               boolean showAllBranch) {
        if(StringTools.isNotBlank(branchName)) { // 过滤分支时不分页
            page = 1;
            pageSize = 10000;
        }

        AdminUserLoginContext adminUserLoginContext = AdminUserLoginInterceptor.getAdminUserLoginContext();
        boolean isAdmin = adminUserLoginContext.isAdmin();
        Long loginUserId = adminUserLoginContext.getUserId();
        PageData<GitRepositoryVO> pageData = repositoryService.getPage(page, pageSize, name, loginUserId, isAdmin);

        Map<Long, Map<String, Object>> results = new ConcurrentHashMap<>();
        List<Future<?>> futures = new ArrayList<>();
        for (final GitRepositoryVO o : pageData.getData()) {
            futures.add(fetchGitThreadPool.submit(() -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", o.getId());
                map.put("name", o.getName());
                // 由于github.com可能用了代理，所以这里处理一下url
                String url = o.getUrl();
                int index = url.indexOf("https://github.com");
                if (index > 0) { // 一定要是>0，才表示https://github.com之前还有字符串
                    url = url.substring(index);
                }

                map.put("url", url);
                map.put("username", o.getUsername());
                // map.put("password", o.getPassword()); // 不要把密码传给前端
                map.put("createTime", o.getCreateTime());
                map.put("droneBaseUrl", o.getDroneBaseUrl());
                map.put("apiPomLocation", o.getApiPomLocation());
                map.put("seq", o.getSeq());
                map.put("developBuildStatus", o.getLastDevelopBuildStatus());

                try {
                    if (forceUpdate) {
                        HiSpeedCacheContext.tryForceRefreshOnce(); // 这里用try是因为极端环境下，强制刷新可能拿不到锁，此时就拿不到值，因此可以降级为走缓存
                    }
                    List<GitBranchInfoDTO> developList = jGitService.getDevelopListWithCommitId(o, showAllBranch);

                    if(StringTools.isNotBlank(branchName)) { // 过滤分支名
                        developList = ListUtils.filter(developList, o1 -> o1.getName().contains(branchName.trim()));
                        if(developList.isEmpty()) {
                            return true;
                        }
                    }
                    ListUtils.sortAscNullLast(developList, GitBranchInfoDTO::getName); // 分支名称排序

                    List<GitBranchConfigDO> gitBranchConfigs = o.getGitBranchConfigs();
                    Map<String, GitBranchConfigDO> branchConfigMap = ListUtils.toMap(gitBranchConfigs, b -> b.getBranchName(), b -> b);

                    map.put("developList", ListUtils.transform(developList, a -> {
                        List<Map<String, Object>> tags = new ArrayList<>();

                        for (GitTagsVO gitTagsVO : o.getGitTags()) {
                            for (GitIngressBranchDO branchDO : gitTagsVO.getGitIngressBranchDOList()) {
                                if (a.getName().equals(branchDO.getBranchName())) {
                                    String tagName = gitTagsVO.getTagName();
                                    if (tagName.startsWith(Constants.RELEASE)) {
                                        tagName = tagName.substring(Constants.RELEASE.length());
                                    }

                                    boolean newCommit = false;
                                    if (StringTools.isNotBlank(branchDO.getCommitId()) && StringTools.isNotBlank(a.getCommitId())) {
                                        if (!branchDO.getCommitId().equalsIgnoreCase(a.getCommitId())) {
                                            newCommit = true;
                                        }
                                    }

                                    // 看距离上次打包的master有没有新的提交
                                    boolean newMaster = false;
                                    String lastMasterCommitId = gitTagsVO.getIngressMasterCommmitId();
                                    if (StringTools.isNotBlank(lastMasterCommitId)) {
                                        if (!lastMasterCommitId.equalsIgnoreCase(a.getMasterCommitId())) {
                                            newMaster = true;
                                        }
                                    }

                                    String buildStatus = gitTagsVO.getBuildStatus();
                                    String scanError = gitTagsVO.getScanMaxErrorLevel();
                                    String scanErrMsg = gitTagsVO.getScanErrorMsg();

                                    tags.add(MapUtils.of("name", tagName,
                                            "newCommit", newCommit,
                                            "newMaster", newMaster,
                                            "isScanError", StringTools.isNotBlank(scanError),
                                            "scanErrMsg", scanErrMsg,
                                            "buildStatus", buildStatus,
                                            "isLock", gitTagsVO.getIsLock())
                                    );
                                }
                            }
                        }

                        GitIngressBranchDO branchDO = null;
                        for (GitIngressBranchDO br : o.getLastDevelopBranch()) {
                            if (br.getBranchName().equals(a.getName())) {
                                branchDO = br;
                            }
                        }

                        Map<String, Object> m = new HashMap<>();
                        m.put("name", a.getName());
                        m.put("tags", tags);
                        m.put("creator", o.getCreatorByBranch(a.getName()));
                        m.put("aheadCount", a.getAheadCount() == null || a.getAheadCount() == 0 ? "0" : "+" + a.getAheadCount());
                        m.put("behindCount", a.getBehindCount() == null || a.getBehindCount() == 0 ? "0" : -a.getBehindCount());

                        // 显示分支自上次发布develop时候，是否有更新了
                        m.put("newCommit", false);
                        if (branchDO != null && StringTools.isNotBlank(branchDO.getCommitId())) {
                            if (StringTools.isNotBlank(a.getCommitId())) {
                                if (!a.getCommitId().equalsIgnoreCase(branchDO.getCommitId())) {
                                    m.put("newCommit", true);
                                    m.put("oldCommitId", branchDO.getCommitId());
                                    m.put("newCommitId", a.getCommitId());
                                }
                            }
                        }

                        // 显示是否自动合并master以及最后一次是否成功
                        GitBranchConfigDO gitBranchConfigDO = branchConfigMap.get(a.getName());
                        if (gitBranchConfigDO != null) {
                            m.put("isAutoMergeMaster", gitBranchConfigDO.getIsAutoMergeMaster());
                            m.put("isMergeMasterFail", gitBranchConfigDO.getIsMergeMasterFail());
                            m.put("mergeMasterFailMsg", gitBranchConfigDO.getMergeMasterFailMsg());
                            m.put("isAutoMergeRemote", gitBranchConfigDO.getIsAutoMergeRemote() != null && gitBranchConfigDO.getIsAutoMergeRemote());
                            m.put("remoteUrl", gitBranchConfigDO.getRemoteUrl());
                            m.put("isMergeRemoteFail", gitBranchConfigDO.getIsMergeRemoteFail() != null && gitBranchConfigDO.getIsMergeRemoteFail());
                        } else {
                            m.put("isAutoMergeMaster", false);
                            m.put("isMergeMasterFail", false);
                            m.put("isAutoMergeRemote", false);
                        }

                        return m;
                    }));
                } catch (Exception e) {
                    log.error("repo get page error", e);
                }

                results.put(o.getId(), map);
                return true;
            }));
        }

        futures.forEach(o -> {
            try {
                o.get();
            } catch (Exception ignored) {
            }
        });

        // 过滤处理后，没有对应数据的仓库（实际上就是被分支过滤掉了）
        pageData.setData(ListUtils.filter(pageData.getData(), o -> results.get(o.getId()) != null));

        Map<String, Object> data = PageUtils.trans(pageData, o -> results.get(o.getId()));

        List<String> droneBaseUrls = gitPermissionService.getDroneBaseUrls();
        data.put("droneBaseUrls", droneBaseUrls);
        data.put("isCheckPermission", gitPermissionService.isCheckPermission());

        // 是否同步构建状态
        data.put("isSyncBuildStatus", ListUtils.isNotEmpty(droneBaseUrls)); // 如果后续有扩展其它的CI，这里需要修改

        return WebJsonBean.ok(data);
    }

    @Permission(value = "EditGit", name = "Git仓库新增编辑删除")
    @RequestMapping("add_or_update")
    @Synchronized(waitLockMillisecond = 30000)
    @Transactional
    public WebJsonBean addOrUpdate(GitRepositoryDO gitRepositoryDO) throws Exception {
        WebCheckUtils.assertNotNull(gitRepositoryDO, "缺少修改的对象参数");
        WebCheckUtils.clearBaseInfo(gitRepositoryDO);
        WebCheckUtils.assertNotNull(gitRepositoryDO.getName(), "仓库名不能为空");
        WebCheckUtils.assertNotNull(gitRepositoryDO.getUrl(), "仓库地址不能为空");
        WebCheckUtils.assertNotNull(gitRepositoryDO.getUsername(), "仓库用户名不能为空");

        // 密码为空白表示不更新
        if(StringTools.isBlank(gitRepositoryDO.getPassword())) {
            gitRepositoryDO.setPassword(null);
        }

        String url = gitRepositoryDO.getUrl();
        if(!(url.startsWith("http://") || url.startsWith("https://"))) {
            return WebJsonBean.fail("仓库地址暂时只支持http://或https://协议");
        }

        // 查询仓库名称是否已经存在，这里的高并发由分布式锁保证，因为这个url操作调用频率很低
        gitRepositoryDO.setName(gitRepositoryDO.getName().trim());
        GitRepositoryDO repoDO = repositoryService.getByName(gitRepositoryDO.getName());
        if(gitRepositoryDO.getId() == null) { // 新增
            if (repoDO != null && gitRepositoryDO.getName().equalsIgnoreCase(repoDO.getName())) {
                return WebJsonBean.fail("仓库名称已存在");
            }
        } else { // 修改
            if (repoDO != null && gitRepositoryDO.getId() != repoDO.getId() &&
                    gitRepositoryDO.getName().equalsIgnoreCase(repoDO.getName())) {
                return WebJsonBean.fail("仓库名称已存在");
            }
        }

        // 处理下droneURL，去掉最后的/
        String droneBaseUrl = gitRepositoryDO.getDroneBaseUrl();
        if (StringTools.isNotBlank(droneBaseUrl) && droneBaseUrl.endsWith("/")) {
            droneBaseUrl = droneBaseUrl.substring(0, droneBaseUrl.length() - 1);
            gitRepositoryDO.setDroneBaseUrl(droneBaseUrl);
        }

        repositoryService.insertOrUpdate(gitRepositoryDO);

        // 重新从数据库查回来，因为前端交互的密码被隐藏了
        gitRepositoryDO = repositoryService.getById(gitRepositoryDO.getId());
        gitCloneService.cloneNew(gitRepositoryDO);

        return WebJsonBean.ok();
    }

    @Permission(value = "EditGit", name = "Git仓库新增编辑删除")
    @RequestMapping("delete")
    public WebJsonBean delete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(repositoryService.deleteById(id));
    }

    @RequestMapping("get_dev_list")
    public WebJsonBean getDevelops(Long id) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        List<String> developList = jGitService.getDevelopList(gitRepositoryDO);
        List<Map<String, Object>> data = ListUtils.transform(developList, p -> {
            Map<String, Object> m = new HashMap<>();
            m.put("name", p);
            m.put("id", id);
            return m;
        });
        return WebJsonBean.ok(data);
    }

    @RequestMapping("get_dev_list_for_develop")
    public WebJsonBean getDevelopsForDevelop(Long id) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        List<String> developList = jGitService.getDevelopList(gitRepositoryDO);
        Map<String, Object> result = new HashMap<>();
        result.put("develops", developList);
        GitIngressVO lastOne = gitIngressService.getLastOne(id, IngressTypeEnum.DEVELOP);
        result.put("checkList", new ArrayList<>());
        if (lastOne != null) {
            List<GitIngressBranchDO> gitIngressBranchDOList = lastOne.getGitIngressBranchDOList();
            List<String> checkList = new ArrayList<>();
            for (GitIngressBranchDO gitIngressBranchDO : gitIngressBranchDOList) {
                if (developList.contains(gitIngressBranchDO.getBranchName())) {
                    checkList.add(gitIngressBranchDO.getBranchName());
                }
            }
            result.put("checkList", checkList);
        }
        return WebJsonBean.ok(result);
    }

    @RequestMapping("get_tag_list")
    public WebJsonBean getTags(Long id) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        List<String> tags = jGitService.getTagListWithoutMerged(gitRepositoryDO);
        VersionUtils.sortVersionAsc(tags);
        return WebJsonBean.ok(tags);
    }

    @RequestMapping("get_release_info")
    public WebJsonBean getReleaseInfo(Long id) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        List<String> developList = jGitService.getDevelopList(gitRepositoryDO);
        List<String> tagList = jGitService.getAllTagList(gitRepositoryDO);
        Map<String, Object> result = new HashMap<>();
        result.put("developList", developList);

        VersionUtils.sortVersionDesc(tagList);
        // 排序后只取前20条
        tagList = tagList.subList(0, Math.min(20, tagList.size()));

        // 查询标签的锁定信息
        Map<String, Boolean> locks = lockReleaseService.queryLocks(id, tagList);

        // 查询标签对应分支
        Map<String, GitIngressVO> tagBranches = gitIngressService.getTagBranches(id, tagList);

        List<Map<String, Object>> tags = ListUtils.transform(tagList,
                o -> {
                    GitIngressVO v = tagBranches.get(o);
                    return MapUtils.of("tag", o,
                            "canLock", locks.containsKey(o),
                            "isLock", locks.get(o),
                            "tagCreateUser", v == null ? "" : v.getCreateUserName(),
                            "tagBranches", v == null ? new ArrayList<>() :
                                    ListUtils.transform(v.getGitIngressBranchDOList(), g -> g.getBranchName()));
                });

        result.put("tagList", tags);
        return WebJsonBean.ok(result);
    }

    @Permission(value = "LockRelase", name = "锁定发布分支")
    @PostMapping("lock_release")
    public WebJsonBean lockRelease(Long id, String tag, Boolean isLock) {
        WebCheckUtils.assertNotNull(id, "仓库id必须提供");
        WebCheckUtils.assertNotBlank(tag, "锁定标签必须提供");
        WebCheckUtils.assertNotNull(isLock, "锁定状态必须提供");

        if(isLock) {
            lockReleaseService.lock(id, tag);
        } else {
            lockReleaseService.unlock(id, tag);
        }
        return WebJsonBean.ok();
    }

    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @Permission(value = "DeployDevelop", name = "Git发布开发环境")
    @RequestMapping("deploy_develop")
    public WebJsonBean deployDevelop(Long id, @JsonParam("develops") List<String> develops) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotNull(develops, "缺少参数develops");
        if (develops.size() == 0) {
            return WebJsonBean.fail("未选择发布分支");
        }

        // 排序分支名称
        ListUtils.sortAscNullLast(develops, o -> o);

        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        jGitService.deployDevelop(gitRepositoryDO, develops);
        return WebJsonBean.ok();
    }

    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @Permission(value = "DeployRelease", name = "Git发布测试环境")
    @RequestMapping("deploy_release")
    public WebJsonBean developRelease(Long id, @JsonParam("develops") List<String> develops,
                                      String tag) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotNull(develops, "缺少参数develops");
        WebCheckUtils.assertNotEmpty(tag, "缺少参数tag");
        if (develops.size() == 0) {
            return WebJsonBean.fail("未选择发布分支");
        }
        ListUtils.sortAscNullLast(develops, o -> o); // 排序分支名称

        if (!tag.startsWith(Constants.RELEASE)) {
            tag = Constants.RELEASE + tag;
        }

        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");

        boolean locked = lockReleaseService.isLocked(id, tag);
        if (locked) {
            return WebJsonBean.fail("该发布版本已经被锁定，不允许以该版本发布");
        }

        CheckRuleLevelEnum checkEnum = jGitService.developRelease(gitRepositoryDO, develops, tag);
        if (checkEnum == null || checkEnum == CheckRuleLevelEnum.INFO) {
            return WebJsonBean.ok();
        } else {
            return WebJsonBean.fail("发布成功，但是代码扫描出问题，级别：" + checkEnum.getName());
        }
    }

    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @RequestMapping("/release_compare")
    public WebJsonBean<?> releaseCompare(Long id, @JsonParam("develops") List<String> develops,
                                      String tag) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotNull(develops, "缺少参数develops");
        WebCheckUtils.assertNotEmpty(tag, "缺少参数tag");
        if (develops.isEmpty()) {
            return WebJsonBean.fail("未选择发布分支");
        }

        tag = tag.startsWith(Constants.RELEASE) ? tag : Constants.RELEASE + tag;

        ListUtils.sortAscNullLast(develops, o -> o); // 排序分支名称

        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");

        jGitService.compareRelease(gitRepositoryDO, develops, tag);

        String url = gitRepositoryDO.getUrl();
        int index = gitRepositoryDO.getUrl().lastIndexOf('.');
        if (index != -1) {
            url = gitRepositoryDO.getUrl().substring(0, index);
        }
        url += "/compare/" + tag + "..." + Constants.TAG_TEMP_NEW_RELEASE;
        return WebJsonBean.ok(url);
    }

    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @Permission(value = "MergeToMaster", name = "Git合并至Master")
    @RequestMapping("merge_to_master")
    public WebJsonBean mergeToMaster(Long id, String tags, boolean checked, boolean forceMerge) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotBlank(tags, "缺少tag");
        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);

        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        jGitService.mergeToMaster(gitRepositoryDO, tags, checked, forceMerge);
        return WebJsonBean.ok();
    }

    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @RequestMapping("check_dev_conflict")
    public WebJsonBean checkDevConflict(Long id, @JsonParam("develops") List<String> develops) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotNull(develops, "缺少参数develops");
        if (develops.size() == 0) {
            return WebJsonBean.fail("未选择分支");
        }

        // 排序分支名称
        ListUtils.sortAscNullLast(develops, o -> o);

        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        ConflictResultDTO conflicts = jGitService.checkConflict(gitRepositoryDO, develops);

        return WebJsonBean.ok(conflicts);
    }

    @RequestMapping("get_tag_to_list")
    public WebJsonBean getTagToList(Long id, String tag) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotBlank(tag, "缺少参数tag");
        GitIngressDO lastByName = gitIngressService.getLastReleaseByName(id, tag);
        WebCheckUtils.assertNotNull(lastByName, "无发布历史");
        List<GitIngressBranchDO> list = gitIngressBranchService.getList(lastByName.getId());
        if (list == null || list.size() == 0) {
            return WebJsonBean.fail("由于历史原因，无相关分支！");
        }
        List<String> result = ListUtils.transform(list, o -> o.getBranchName());
        return WebJsonBean.ok(result);
    }

    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @Permission(value = "DeleteTag", name = "Git删除Tag")
    @RequestMapping("delete_tag")
    public WebJsonBean deleteTag(Long id, @JsonParam("tags") List<String> tags) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotNull(tags, "缺少参数tags");
        if (tags.size() == 0) {
            return WebJsonBean.fail("未选择要删除的tag");
        }
        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        return WebJsonBean.ok(jGitService.deleteTag(gitRepositoryDO, tags));
    }

    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @RequestMapping("tag_compare")
    public WebJsonBean tagCompare(Long id, String tags) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotBlank(tags, "缺少tag");

        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");

        String url = gitRepositoryDO.getUrl();
        int index = gitRepositoryDO.getUrl().lastIndexOf('.');
        if (index != -1) {
            url = gitRepositoryDO.getUrl().substring(0, index);
        }
        url += "/compare/" + "master..." + tags;
        return WebJsonBean.ok(url);
    }

}