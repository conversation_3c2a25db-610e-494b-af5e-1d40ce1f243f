package com.pugwoo.branch.git.service.impl;

import com.pugwoo.branch.code_scan.entity.CheckRuleDO;
import com.pugwoo.branch.code_scan.entity.CheckRuleDetailDO;
import com.pugwoo.branch.code_scan.model.CheckRuleBO;
import com.pugwoo.branch.git.enums.ItilErrorCode;
import com.pugwoo.branch.git.model.CheckRuleCreateDTO;
import com.pugwoo.branch.git.model.CheckRuleDetailCreateDTO;
import com.pugwoo.branch.git.service.ICheckRuleService;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CheckRuleServiceImpl implements ICheckRuleService {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @Override
    public void add(CheckRuleCreateDTO gitCheckCreateDTO) {
        CheckRuleDO gitCheckRuleDO = new CheckRuleDO();
        gitCheckRuleDO.setName(gitCheckCreateDTO.getName());
        if (dbHelper.insert(gitCheckRuleDO) != 1) {
            throw new AdminInnerException(ItilErrorCode.CHECK_RULE_CREATE_ERROR);
        }
        List<CheckRuleDetailDO> gitCheckRuleDetailDOList = new ArrayList<>();
        for (CheckRuleDetailCreateDTO gitCheckRuleDetailCreateDTO : gitCheckCreateDTO.getCheckDetailCreateDTOList()) {
            CheckRuleDetailDO gitCheckRuleDetailDO = new CheckRuleDetailDO();
            gitCheckRuleDetailDO.setRuleId(gitCheckRuleDO.getId());
            gitCheckRuleDetailDO.setContentRegex(gitCheckRuleDetailCreateDTO.getContent());
            gitCheckRuleDetailDO.setType(gitCheckRuleDetailCreateDTO.getType());
            gitCheckRuleDetailDO.setLevel(gitCheckRuleDetailCreateDTO.getLevel());
            gitCheckRuleDetailDOList.add(gitCheckRuleDetailDO);
        }
        if (dbHelper.insert(gitCheckRuleDetailDOList) < 1) {
            throw new AdminInnerException(ItilErrorCode.CHECK_RULE_CREATE_ERROR);
        }
    }

    @Override
    public PageData<CheckRuleBO> getPage(String name, int page, int pageSize) {
        StringBuilder sql = new StringBuilder();
        List<Object> params = new ArrayList<>();
        if (StringTools.isNotEmpty(name)) {
            sql.append(" where name like ?");
            params.add("%" + name + "%");
        }
        return dbHelper.getPage(CheckRuleBO.class, page, pageSize, sql.toString(), params.toArray());
    }

    @Override
    public CheckRuleBO get(Long id) {
        return dbHelper.getOne(CheckRuleBO.class, " where id = ?", id);
    }


}