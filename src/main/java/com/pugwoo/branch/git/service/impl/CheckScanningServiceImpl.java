package com.pugwoo.branch.git.service.impl;

import com.pugwoo.branch.code_scan.entity.CheckRuleDetailDO;
import com.pugwoo.branch.code_scan.entity.CheckRuleFileDO;
import com.pugwoo.branch.code_scan.entity.CheckScanResultDO;
import com.pugwoo.branch.code_scan.enums.CheckRuleLevelEnum;
import com.pugwoo.branch.code_scan.enums.CheckRuleTypeEnum;
import com.pugwoo.branch.code_scan.model.CheckRuleBO;
import com.pugwoo.branch.git.service.ICheckScanningService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.string.RegexUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class CheckScanningServiceImpl implements ICheckScanningService {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @Override
    @Transactional
    public CheckRuleLevelEnum checkScanning(String filePath, Long ingressId) throws IOException {
        List<CheckRuleBO> rules = dbHelper.getAll(CheckRuleBO.class);
        Set<CheckRuleLevelEnum> checkedLevels = new HashSet<>();

        // 以文件为维度来扫描，减少磁盘读写次数
        List<File> files = IOUtils.listFiles(new File(filePath));
        for(File file : files) {
            String fileContent = null; // 缓存文件内容
            StringBuilder errmsg = new StringBuilder();

            for(CheckRuleBO rule : rules) {
                String matched = getMatched(file.getName(), rule.getCheckFiles());
                if(matched != null) {
                    if(fileContent == null) {
                        fileContent = IOUtils.readAllAndClose(new FileInputStream(file), "utf8");
                    }

                    StringBuilder err = new StringBuilder();
                    for (CheckRuleDetailDO ruleDetail : rule.getCheckRuleDetailDOList()) {
                        if (CheckRuleTypeEnum.MUST_NOT.getCode().equals(ruleDetail.getType())) {
                            if(RegexUtils.getFirstMatchStr(fileContent, ruleDetail.getContentRegex()) != null) {
                                err.append("匹配中:").append(ruleDetail.getContentRegex()).append(";");
                                checkedLevels.add(CheckRuleLevelEnum.getByCode(ruleDetail.getLevel()));
                            }
                        }
                    }

                    if(!err.isEmpty()) {
                        errmsg.append(err);
                    }
                }
            }

            if(!errmsg.isEmpty()) {
                CheckScanResultDO checkScanResultDO = new CheckScanResultDO();
                checkScanResultDO.setCheckMsg(errmsg.toString());
                checkScanResultDO.setIngressId(ingressId);
                checkScanResultDO.setErrorLevel(getMaxLevel(checkedLevels));
                checkScanResultDO.setFilename(file.getPath());
                dbHelper.insert(checkScanResultDO);
            }
        }

        log.info("check code:{} finish, result status:{}", filePath, getMaxLevel(checkedLevels));
        return getMaxLevelEnum(checkedLevels);
    }

    private CheckRuleLevelEnum getMaxLevelEnum(Set<CheckRuleLevelEnum> sets) {
        if(sets == null) {
            return null;
        }
        if(sets.contains(CheckRuleLevelEnum.ERROR)) {
            return CheckRuleLevelEnum.ERROR;
        }
        if(sets.contains(CheckRuleLevelEnum.WARNING)) {
            return CheckRuleLevelEnum.WARNING;
        }
        if(sets.contains(CheckRuleLevelEnum.INFO)) {
            return CheckRuleLevelEnum.INFO;
        }
        return null;
    }

    private String getMaxLevel(Set<CheckRuleLevelEnum> sets) {
        if(sets == null) {
            return "";
        }
        if(sets.contains(CheckRuleLevelEnum.ERROR)) {
            return CheckRuleLevelEnum.ERROR.getCode();
        }
        if(sets.contains(CheckRuleLevelEnum.WARNING)) {
            return CheckRuleLevelEnum.WARNING.getCode();
        }
        if(sets.contains(CheckRuleLevelEnum.INFO)) {
            return CheckRuleLevelEnum.INFO.getCode();
        }
        return "";
    }

    /**检查一下文件名匹配中的正则表达式，没有匹配中返回null*/
    private static String getMatched(String fileName, List<CheckRuleFileDO> checkFiles) {
        if(checkFiles == null) {
            return null;
        }
        for(CheckRuleFileDO fileDO : checkFiles) {
            if(StringTools.isNotBlank(fileDO.getFileRegex())) {
                if(RegexUtils.isMatch(fileName, fileDO.getFileRegex())) {
                    return fileDO.getFileRegex();
                }
            }
        }
        return null;
    }
}