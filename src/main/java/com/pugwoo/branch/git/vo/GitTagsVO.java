package com.pugwoo.branch.git.vo;

import com.pugwoo.branch.code_scan.entity.CheckScanResultDO;
import com.pugwoo.branch.code_scan.enums.CheckRuleLevelEnum;
import com.pugwoo.branch.git.entity.GitIngressBranchDO;
import com.pugwoo.branch.git.entity.GitIngressDO;
import com.pugwoo.branch.git.entity.GitTagsDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.util.List;

@Data
public class GitTagsVO extends GitTagsDO {

    @RelatedColumn(localColumn = "ingress_id", remoteColumn = "ingress_id")
    private List<GitIngressBranchDO> gitIngressBranchDOList;

    @RelatedColumn(localColumn = "ingress_id", remoteColumn = "id")
    private GitIngressDO gitIngressDO;

    @RelatedColumn(localColumn = "ingress_id", remoteColumn = "ingress_id")
    private List<CheckScanResultDO> checkScanResults;

    public String getIngressMasterCommmitId() {
        if(gitIngressDO == null) {
            return "";
        }
        String masterCommitId = gitIngressDO.getMasterCommitId();
        return masterCommitId == null ? "" : masterCommitId;
    }

    public String getScanMaxErrorLevel() {
        if(checkScanResults == null || checkScanResults.isEmpty()) {
            return "";
        }
        List<CheckRuleLevelEnum> enums = ListUtils.transform(checkScanResults,
                o -> CheckRuleLevelEnum.getByCode(o.getErrorLevel()));
        return getMaxLevel(enums);
    }

    public String getScanErrorMsg() {
        if(checkScanResults == null || checkScanResults.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for(CheckScanResultDO checkScanResultDO : checkScanResults) {
            sb.append("文件:" + checkScanResultDO.getFilename() + ",问题:" + checkScanResultDO.getCheckMsg()).append("\n");
        }
        return sb.toString();
    }

    private String getMaxLevel(List<CheckRuleLevelEnum> sets) {
        if(sets == null) {
            return "";
        }
        if(sets.contains(CheckRuleLevelEnum.ERROR)) {
            return CheckRuleLevelEnum.ERROR.getCode();
        }
        if(sets.contains(CheckRuleLevelEnum.WARNING)) {
            return CheckRuleLevelEnum.WARNING.getCode();
        }
        if(sets.contains(CheckRuleLevelEnum.INFO)) {
            return CheckRuleLevelEnum.INFO.getCode();
        }
        return "";
    }

    public String getBuildStatus(){
        if(gitIngressDO == null) {
            return "";
        }
        return gitIngressDO.getBuildStatus();
    }

}