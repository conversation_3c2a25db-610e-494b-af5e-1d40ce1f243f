package com.pugwoo.branch.git.utils;

import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.git.enums.ItilErrorCode;
import com.pugwoo.branch.git.model.GitBranchInfoDTO;
import com.pugwoo.branch.git.model.GitUserPasswordDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.eclipse.jgit.api.CreateBranchCommand;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.PullCommand;
import org.eclipse.jgit.api.PullResult;
import org.eclipse.jgit.api.ResetCommand;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.errors.RepositoryNotFoundException;
import org.eclipse.jgit.lib.Ref;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.revwalk.RevWalk;
import org.eclipse.jgit.revwalk.RevWalkUtils;
import org.eclipse.jgit.revwalk.filter.RevFilter;
import org.eclipse.jgit.storage.file.FileRepositoryBuilder;
import org.eclipse.jgit.transport.PushResult;
import org.eclipse.jgit.transport.RefSpec;
import org.eclipse.jgit.transport.RemoteRefUpdate;
import org.eclipse.jgit.transport.URIish;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * GitUtils保持工具类方式，是为了更方便本地调试
 */
@Slf4j
public class GitUtils {

    /**
     * 将gitUrl检出到localPath，如果已经存在，则重置并pull更新。
     * 执行完这个方法之后，相当于本地的所有分支、tag和变更都被重置(删除)，tag还原为远程tag，将repo状态重置为SAFE。
     * <br>
     * 【特别说明】这个方法如果不考虑性能的话，就相当于每次都检出到一个新的目录，但是这样性能很差。
     *            这个方法的特点是利用本地已有的git，做远程同步和reset，从而达到全新拉的效果。
     *
     * @param localPath 本地存放目录，注意不是.git目录，而是存放.git的那个目录的路径
     */
    public static boolean cloneNew(String gitUrl, String localPath,
                                   GitUserPasswordDTO password) throws Exception {
        File file = new File(localPath);
        if(file.exists()) { // 处理一下特殊情况：是文件或者空文件夹
            if(file.isFile()) {
                file.delete();
            } else if (file.isDirectory() && Objects.requireNonNull(file.listFiles()).length == 0) {
                file.delete();
            }
        }

        if (file.exists()) {
            Git git = null;
            try {
                git = Git.open(file); // 这里得用Git.open，而不用repo去拿
            } catch (RepositoryNotFoundException e) { // 出现这种情况是因为.git文件不存在，此时删除整个目录，再重来
                FileUtils.deleteDirectory(file);
                return cloneNew(file, gitUrl, password);
            }

            try {
                // 为了避免url发生改变，都再设置一次
                git.remoteSetUrl().setRemoteName("origin")
                        .setRemoteUri(new URIish(gitUrl))
                        .call();
            } catch (Exception e) {
                log.error("set remote-url fail:{}, localPath:{}", gitUrl, localPath);
            }

            if(!getLocalBranches(git).contains("master")) { // 适应有些仓库默认分支不是master的情况
                git.branchCreate()
                        .setName("master")
                        .setUpstreamMode(CreateBranchCommand.SetupUpstreamMode.SET_UPSTREAM)
                        .setStartPoint("origin/master")
                        .setForce(true)
                        .call();
            }
            try {
                git.checkout().setName("master").call();
            } catch (Throwable e) {
                // ignore，先试图切换master，当有冲突时，可能切换不成功，下面再切换一次
            }
            git.reset().setMode(ResetCommand.ResetType.HARD).call(); // reset hard

            git.checkout().setName("master").call();
            git.reset().setMode(ResetCommand.ResetType.HARD).setRef("origin/master").call(); // again, 同时reset到origin/master

            // 删除本地所有的tag
            List<String> localTags = getLocalTags(git);
            List<String> remoteTags = getRemoteTags(git, password);
            // 只有本地有，remoteTags没有的才进行删除
            for(String tag : localTags) {
                if (!remoteTags.contains(tag)) {
                    deleteLocalTag(git, tag);
                }
            }

            // 删除本地master之外的所有的分支
            List<String> localBranches = getLocalBranches(git);
            if(localBranches != null) {
                for(String branch : localBranches) {
                    if(!"master".equals(branch)) {
                        deleteLocalBranch(git, branch);
                    }
                }
            }

            PullResult result = git.pull().setCredentialsProvider(from(password)).setTimeout(60).call(); // 这个会同步回远程tag

            git.getRepository().getConfig().setString("user", null,
                    "name", password.getUsername());
            git.getRepository().getConfig().save();

            git.close();
            return result.isSuccessful();
        } else {
            return cloneNew(file, gitUrl, password);
        }
    }

    /**
     * 通过本地路径path获取仓库实例
     */
    public static Repository getJRepository(String localPath) throws IOException {
        if(localPath == null) {
            return null;
        }
        if(!localPath.endsWith("/.git")) {
            localPath = localPath + "/.git";
        }
        File file = new File(localPath);
        if (!file.exists() || !file.isDirectory()) {
            throw new AdminInnerException(ItilErrorCode.LOCATION_IS_NOT_DIR);
        }
        return new FileRepositoryBuilder().
                setGitDir(file)
                .build();
    }

    /**
     * 设置auto merge的remote url，当已经存在时，会自动删除并重新设置上
     */
    public static void setAutoMergeRemoteUrl(Git git, String remoteName, String remoteUrl)
            throws URISyntaxException, GitAPIException {
        boolean remoteExists = git.getRepository().getConfig().getSubsections("remote").contains(remoteName);
        if (remoteExists) {
            git.remoteRemove().setRemoteName(remoteName).call();
        }

        git.remoteAdd().setName(remoteName).setUri(new URIish(remoteUrl)).call();
    }

    /**
     * fetch远程代码并合并到当前分支上，默认使用rebase方式
     * @param remotePassword 可选
     */
    public static void fetchAndMergeRemote(Git git, String remoteName,
                                           String branchName, GitUserPasswordDTO remotePassword) throws GitAPIException {
        PullCommand pullCommand = git.pull().setRemote(remoteName).setRemoteBranchName(branchName);
        if (remotePassword != null && StringTools.isNotBlank(remotePassword.getUsername())
                && StringTools.isNotBlank(remotePassword.getPassword())) {
            pullCommand = pullCommand.setCredentialsProvider(GitUtils.from(remotePassword));
        }

        pullCommand = pullCommand.setRebase(true);

        PullResult result = pullCommand.setTimeout(60).call();
        if(result == null || !result.isSuccessful()) {
            throw new AdminInnerException(ItilErrorCode.PULL_ERROR);
        }
    }

    public static List<String> getRemoteBranches(Git git, GitUserPasswordDTO password) throws GitAPIException {
        List<String> result = new ArrayList<>();
        Collection<Ref> refs = git.lsRemote()
                .setHeads(true)
                .setTags(false)
                .setCredentialsProvider(from(password))
                .setTimeout(60)
                .call();
        for (Ref ref : refs) {
            String refName = ref.getName();
            if (refName.startsWith("refs/heads/")) {
                refName = refName.substring("refs/heads/".length());
            }
            result.add(refName);
        }
        return result;
    }

    public static List<GitBranchInfoDTO> getRemoteBranchesWithCount(Git git, GitUserPasswordDTO password) throws GitAPIException {
        Collection<Ref> refs = git.lsRemote()
                .setHeads(true)
                .setTags(false)
                .setCredentialsProvider(from(password))
                .setTimeout(60)
                .call();

        Ref master = null;
        for (Ref ref : refs) {
            if (ref.getName().equals("refs/heads/master")) {
                master = ref;
            }
        }

        Repository repository = git.getRepository();

        List<GitBranchInfoDTO> result = new ArrayList<>();
        for (Ref ref : refs) {
            String refName = ref.getName();
            if (refName.startsWith("refs/heads/")) {
                refName = refName.substring("refs/heads/".length());
            }

            GitBranchInfoDTO dto = new GitBranchInfoDTO();
            dto.setName(refName);

            if (!refName.equals("master") && master != null) {
                Count count = calculateDivergence(repository, ref, master);
                if (count != null) {
                    dto.setAheadCount(count.getAheadCount());
                    dto.setBehindCount(count.getBehindCount());
                }
            }

            result.add(dto);
        }

        return result;
    }
    
    /**
     * 回退分支版本
     * @param git        git
     * @param password   鉴权用
     * @param branchName 分支名
     * @param ahead      回退几个版本
     */
    public static void resetCommit(Git git, GitUserPasswordDTO password, String branchName, int ahead) throws Exception {
        Ref ref = GitUtils.checkoutRemoteBranchAndPull(git, password, branchName);
        git.reset()
                .setMode(ResetCommand.ResetType.HARD)
                .setRef("HEAD^" + ahead)
                .call();
        pushForceToRemote(git, ref, password);
    }
    
    @Data
    private static class Count {
        private int aheadCount;
        private int behindCount;
    }

    private static Count calculateDivergence(Repository repository, Ref local, Ref tracking) {
        try (RevWalk walk = new RevWalk(repository)) {
            RevCommit localCommit = walk.parseCommit(local.getObjectId());
            RevCommit trackingCommit = walk.parseCommit(tracking.getObjectId());
            walk.setRevFilter(RevFilter.MERGE_BASE);
            walk.markStart(localCommit);
            walk.markStart(trackingCommit);
            RevCommit mergeBase = walk.next();
            walk.reset();
            walk.setRevFilter(RevFilter.ALL);
            int aheadCount = RevWalkUtils.count(walk, localCommit, mergeBase);
            int behindCount = RevWalkUtils.count(walk, trackingCommit, mergeBase);
            Count count = new Count();
            count.setAheadCount(aheadCount);
            count.setBehindCount(behindCount);
            return count;
        } catch (Exception e) {
            return null;
        }
    }

    public static List<String> getRemoteTags(Git git, GitUserPasswordDTO password) throws GitAPIException {
        List<String> result = new ArrayList<>();
        Collection<Ref> refs = git.lsRemote()
                .setHeads(false)
                .setTags(true)
                .setCredentialsProvider(from(password))
                .setTimeout(60)
                .call();
        for (Ref ref : refs) {
            int index = ref.getName().lastIndexOf('/');
            if (index != -1) {
                result.add(ref.getName().substring(index + 1));
            }
        }
        return result;
    }

    /**
     * 获取全部本地分支
     *
     * @param
     * @return
     */
    public static List<String> getLocalBranches(Git git) throws GitAPIException {
        List<Ref> refs = git.branchList().call();
        return toRefNames(refs);
    }


    /**
     * 获得指定分支的最后一次提交的commit id
     * @param branchName
     * @param isLocal 是否本地，如果false则表示远程分支
     * @return 如果拿不到则返回空字符串
     */
    public static String getCommitId(Git git, String branchName, boolean isLocal) {
        Map<String, Ref> allRefs = git.getRepository().getAllRefs();
        String refName = (isLocal ? "refs/heads/" : "refs/remotes/origin/") + branchName;
        Ref ref = allRefs.get(refName);
        if(ref == null) {
            return "";
        }
        return ref.getObjectId().getName();
    }

    public static String getTagCommitId(Git git, String tag) {
        Map<String, Ref> allRefs = git.getRepository().getAllRefs();
        String refName = "refs/tags/" + tag;
        Ref ref = allRefs.get(refName);
        if(ref == null) {
            return "";
        }
        return ref.getObjectId().getName();
    }

    /**
     * 从远程master新建本地分支，如果分支已经存在，则创建失败抛出异常org.eclipse.jgit.api.errors.RefAlreadyExistsException
     * @return 返回分支的引用
     */
    public static Ref createLocalBranchFromMasterAndCheckout(Git git, String branchName) throws GitAPIException {
        return createLocalBranchAndCheckout(git, "refs/remotes/origin/master", branchName);
    }

    /**
     * checkout出远程分支并pull同步
     * @param branchName 会自动删除已存在的本地同名分支
     */
    public static Ref checkoutRemoteBranchAndPull(Git git, GitUserPasswordDTO password, String branchName)
        throws Exception {
        if(branchName.equals("master")) { // master直接checkout pull
            git.checkout().setName("master").call();
            pull(git, password);
            return git.checkout().setName("master").call(); // 再checkout一次拿最新commit id
        }
        deleteLocalBranch(git, branchName);
        createLocalBranchAndCheckout(git, "refs/remotes/origin/" + branchName, branchName);
        boolean result = pull(git, password);
        if(!result) {
            throw new AdminInnerException(ItilErrorCode.PULL_ERROR);
        }
        Ref ref = git.checkout().setName(branchName).call(); // 再checkout一次拿最新commit id
        return ref;
    }

    /**
     * 从指定的起始点startPoint创建本地分支<br>
     * <br>
     * 如果分支已经存在，则创建失败抛出异常org.eclipse.jgit.api.errors.RefAlreadyExistsException。<br>
     * 如果起始点找不到，抛org.eclipse.jgit.api.errors.RefNotFoundException异常。<br>
     * <br>
     * 当startPoint是远程分支时，创建的本地分支会自动track远程分支；而本地分支则不会(包括master)，也即不能pull<br>
     *
     * @param startPoint 起始点,例如refs/heads/分支名称 ; refs/tags/标签名称 ; refs/remotes/origin/远程分支
     * @return 返回分支的引用
     */
    public static Ref createLocalBranchAndCheckout(
            Git git, String startPoint, String branchName) throws GitAPIException {
        git.branchCreate().setName(branchName).setStartPoint(startPoint).call();
        return git.checkout().setName(branchName).call(); // 创建新分支之后需要checkout，不然代码不同步
    }

    /**
     * 当前git当前分支拉代码pull
     */
    public static boolean pull(Git git, GitUserPasswordDTO password) throws Exception {
        PullResult call = git.pull().setCredentialsProvider(GitUtils.from(password)).setTimeout(60).call();
        return call.isSuccessful();
    }
    
    /**
     * 将分支或标签推送到远程仓库
     */
    public static void pushForceToRemote(Git git, Ref ref, GitUserPasswordDTO password) throws GitAPIException {
        Iterable<PushResult> results = git.push().setForce(true).add(ref).setCredentialsProvider(from(password)).setTimeout(60).call();
        if(!isSuccessful(results)) {
            throw new AdminInnerException(ItilErrorCode.PUSH_ERROR);
        }
    }

    public static void pushMasterToRemote(Git git, GitUserPasswordDTO password) throws GitAPIException {
        RefSpec refSpec = new RefSpec()
                .setDestination("refs/heads/master");
        Iterable<PushResult> results = git.push().setRefSpecs(refSpec)
                .setRemote("origin")
                .setCredentialsProvider(GitUtils.from(password))
                .setTimeout(60).call();
        if(!isSuccessful(results)) {
            throw new AdminInnerException(ItilErrorCode.PUSH_ERROR);
        }
    }

    /**
     * 从本地指定的srcBranchName分支创建本地分支并同步至远程仓库
     */
    public static void createLocalBranchToRemote(Git git, GitUserPasswordDTO password,
                                                        String srcBranchName, String branchName) throws Exception {
        if(!srcBranchName.equals("master")) {
            checkoutRemoteBranchAndPull(git, password, srcBranchName);
        } else {
            git.checkout().setName("master").call();
        }

        git.branchCreate().setName(branchName).call();
        Ref ref = git.checkout().setName(branchName).call(); // 创建新分支之后需要checkout，不然代码不同步
        Iterable<PushResult> results = git.push().add(ref).setCredentialsProvider(from(password)).setTimeout(60).call();
        if(!isSuccessful(results)) {
            throw new AdminInnerException(ItilErrorCode.PUSH_ERROR);
        }
    }

    /**
     * 在git当前分支创建ref(分支)并推送到远程
     */
    public static void refBranchAndPushRemote(Git git, GitUserPasswordDTO password, String branchName) throws Exception {
        RefSpec refSpec = new RefSpec()
                .setDestination("refs/heads/" + branchName);
        Iterable<PushResult> results =git.push().setRefSpecs(refSpec)
                .setRemote("origin")
                .setCredentialsProvider(GitUtils.from(password))
                .setTimeout(60).call();
        if(!isSuccessful(results)) {
            throw new AdminInnerException(ItilErrorCode.PUSH_ERROR);
        }
    }

    /**
     * 在git当前分支创建ref(分支)并推送到远程
     */
    public static void refBranchAndPushRemoteForce(Git git, GitUserPasswordDTO password, String branchName) throws Exception {
        RefSpec refSpec = new RefSpec()
                .setDestination("refs/heads/" + branchName);
        Iterable<PushResult> results =git.push().setRefSpecs(refSpec)
                .setRemote("origin")
                .setCredentialsProvider(GitUtils.from(password))
                .setForce(true)
                .setTimeout(60).call();
        if(!isSuccessful(results)) {
            throw new AdminInnerException(ItilErrorCode.PUSH_ERROR);
        }
    }

    /**
     * 在git当前分支打tag并推送到远程
     */
    public static void tagAndPushRemote(Git git, GitUserPasswordDTO password, String tagName) throws Exception {
        Ref tagRef = git.tag().setName(tagName).call();
        Iterable<PushResult> results = git.push().add(tagRef)
                .setCredentialsProvider(GitUtils.from(password))
                .setTimeout(60).call();
        if(!isSuccessful(results)) {
            throw new AdminInnerException(ItilErrorCode.PUSH_ERROR);
        }
    }

    /**
     * 删除本地分支，如果本地分支不存在，也认为是成功的
     */
    public static boolean deleteLocalBranch(Git git, String localBranchName) throws GitAPIException {
        // 如果当前checkout的分支是localBranchName，那是没法删除的会抛异常
        // 【重要】这里千万不要checkout到master分支以便删除localBranchName，容易出问题
        git.branchDelete().setForce(true).setBranchNames(localBranchName).call();
        return true;
    }

    /**
     * 直接删除远程仓库分支，如果远程分支不存在，就不操作。<br>
     * 说明：remote是origin
     * @throws AdminInnerException 失败抛出异常
     */
    public static void deleteRemoteBranch(Git git, GitUserPasswordDTO password,
                                             String remoteBranchName) throws Exception {

        List<String> remoteBranches = GitUtils.getRemoteBranches(git, password);
        if(!remoteBranches.contains(remoteBranchName)) {
            return;
        }

        RefSpec refSpec = new RefSpec()
                .setSource(null)
                .setDestination("refs/heads/" + remoteBranchName);
        Iterable<PushResult> results = git.push().setRefSpecs(refSpec).setRemote("origin")
                .setCredentialsProvider(from(password)).setTimeout(60).call();
        boolean success = isSuccessful(results);
        if(!success) {
            throw new AdminInnerException(ItilErrorCode.DELETE_REMOTE_BRANCH_FAIL);
        }
    }

    /**
     * 获取本地所有tag
     */
    public static List<String> getLocalTags(Git git) throws GitAPIException {
        List<Ref> refs = git.tagList().call();
        return toRefNames(refs);
    }

    /**
     * 删除本地标签，如果不存在则认为是成功
     */
    public static boolean deleteLocalTag(Git git, String localTagName) throws GitAPIException {
        // 已经确认过，不需要事先删除
        git.tagDelete().setTags(localTagName).call();
        return true;
    }

    /**
     * 删除远程仓库tag，如果不存在则认为成功。
     */
    public static boolean deleteRemoteTag(Git git, GitUserPasswordDTO password,
                                          String remoteTagName) throws GitAPIException {

        List<String> remoteBranches = GitUtils.getRemoteTags(git, password);
        if(remoteBranches == null || !remoteBranches.contains(remoteTagName)) {
            return true;
        }

        RefSpec refSpec = new RefSpec()
                .setSource(null)
                .setDestination("refs/tags/" + remoteTagName);
        git.push().setRefSpecs(refSpec).setRemote("origin")
                .setCredentialsProvider(from(password)).setTimeout(60).call();

        return true;
    }

    public static UsernamePasswordCredentialsProvider from(GitUserPasswordDTO password) {
        return new UsernamePasswordCredentialsProvider(password.getUsername(), password.getPassword());
    }

    private static List<String> toRefNames(List<Ref> refs) {
        List<String> branchNames = new ArrayList<>();
        for (Ref ref : refs) {
            int index = ref.getName().lastIndexOf('/');
            if (index != -1) {
                branchNames.add(ref.getName().substring(index + 1));
            }
        }
        return branchNames;
    }

    private static boolean isSuccessful(Iterable<PushResult> results) {
        for(PushResult result : results) {
            Collection<RemoteRefUpdate> remoteUpdates = result.getRemoteUpdates();
            for(RemoteRefUpdate remoteRefUpdate : remoteUpdates) {
                if(remoteRefUpdate.getStatus() != RemoteRefUpdate.Status.OK
                        && remoteRefUpdate.getStatus() != RemoteRefUpdate.Status.UP_TO_DATE) {
                    return false;
                }
            }
        }
        return true;
    }

    private static boolean cloneNew(File file, String gitUrl, GitUserPasswordDTO password) throws Exception {
        boolean success = file.mkdirs();
        if(!success) {
            throw new AdminInnerException(ItilErrorCode.CREATE_DIR_FAIL);
        }
        Git git = Git.cloneRepository()
                .setURI(gitUrl)
                .setDirectory(file)
                .setCloneAllBranches(true)
                .setCredentialsProvider(from(password))
                .setBranch("master")
                .setTimeout(60)
                .call();

        List<String> remoteBranches = getRemoteBranches(git, password);
        if(ListUtils.isEmpty(remoteBranches)) { // 限制git仓库必须先初始化
            FileUtils.forceDelete(file);
            throw new AdminInnerException(ItilErrorCode.GIT_NOT_INIT_YET);
        }

        git.getRepository().getConfig().setString("user", null,
                "name", password.getUsername());
        git.getRepository().getConfig().save();

        git.close();
        return true;
    }
}
