package com.pugwoo.branch.code_scan.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.util.Date;

@Data
@Table("check_rule_file")
public class CheckRuleFileDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 规则id<br/>Column: [rule_id] */
    @Column(value = "rule_id")
    private Long ruleId;

    /** 文件名称，可选<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 文件名称正则表达式<br/>Column: [file_regex] */
    @Column(value = "file_regex")
    private String fileRegex;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private Date createTime;

}