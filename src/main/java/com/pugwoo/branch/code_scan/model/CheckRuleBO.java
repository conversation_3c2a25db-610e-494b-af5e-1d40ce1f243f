package com.pugwoo.branch.code_scan.model;

import com.pugwoo.branch.code_scan.entity.CheckRuleDO;
import com.pugwoo.branch.code_scan.entity.CheckRuleDetailDO;
import com.pugwoo.branch.code_scan.entity.CheckRuleFileDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.util.List;

@Data
public class CheckRuleBO extends CheckRuleDO {

    @RelatedColumn(localColumn = "id",remoteColumn = "rule_id")
    private List<CheckRuleDetailDO> checkRuleDetailDOList;

    @RelatedColumn(localColumn = "id", remoteColumn = "rule_id")
    private List<CheckRuleFileDO> checkFiles;

}