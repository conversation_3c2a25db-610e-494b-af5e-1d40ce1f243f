package com.pugwoo.branch.chart.web.req;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CreateSandboxReq {

    /**是否是临时图表，当是临时图标时，它只会在redis中存留10分钟*/
    private Boolean tmp;

    /**图表的名称，建议提供*/
    private String name;

    /**组件库，例如echarts*/
    private String library;

    /**组件名称，例如3Dbar*/
    private String charts;

    /**表头，弱化表头校验*/
    private List<String> titles;

    /**数据*/
    private List<List<String>> data;

    /**配置项*/
    private Map<String, Object> configs;

}
