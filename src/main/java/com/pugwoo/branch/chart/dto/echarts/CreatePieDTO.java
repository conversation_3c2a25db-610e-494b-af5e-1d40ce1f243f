package com.pugwoo.branch.chart.dto.echarts;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.chart.dto.valuedto.SingleDimValueDTO;
import com.pugwoo.branch.chart.utils.ChartCommonUtils;
import com.pugwoo.branch.chart.web.req.CreateSandboxReq;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 创建饼图所需的数据和配置
 */
@Data
public class CreatePieDTO {

    @NotNull
    private List<SingleDimValueDTO> data = new ArrayList<>();
    @NotNull
    private String title = "";
    /**是否显示图例*/
    @NotNull
    private Boolean showLegend = true;
    /**是否显示数值*/
    @NotNull
    private Boolean showValue = false;
    /**是否显示百分比*/
    @NotNull
    private Boolean showPercent = false;

    public static CreatePieDTO from(CreateSandboxReq req) {
        CreatePieDTO createPieDTO = new CreatePieDTO();
        Integer columns = ChartCommonUtils.determinateColumn(req.getData());
        if (columns != null) {
            List<SingleDimValueDTO> data = new ArrayList<>();
            if (columns == 2) {
                for (List<String> row : req.getData()) {
                    SingleDimValueDTO d = new SingleDimValueDTO();
                    d.setDim(row.getFirst());
                    d.setValue(NumberUtils.parseBigDecimal(row.get(1)));
                    data.add(d);
                }
            } else {
                throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "表头列数只支持2列，实际列数：" + req.getTitles().size());
            }
            createPieDTO.setData(data);
        }

        // 处理参数
        createPieDTO.setShowLegend(ChartCommonUtils.getConfig(req.getConfigs(), "showLegend", createPieDTO.getShowLegend(),
                ChartCommonUtils::parseBoolean, null));
        createPieDTO.setShowValue(ChartCommonUtils.getConfig(req.getConfigs(), "showValue", createPieDTO.getShowValue(),
                ChartCommonUtils::parseBoolean, null));
        createPieDTO.setShowPercent(ChartCommonUtils.getConfig(req.getConfigs(), "showPercent", createPieDTO.getShowPercent(),
                ChartCommonUtils::parseBoolean, null));

        createPieDTO.setTitle(req.getName() == null ? "" : req.getName());
        return createPieDTO;
    }
}