package com.pugwoo.branch.chart.dto.valuedto;

import com.pugwoo.branch.chart.utils.ChartCommonUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 这是一个非常通用的画图数据结构，适用于折线图、堆叠图、柱状图等。
 * 它只有一个维度（多维度数据也可以聚合成单维度）。
 */
@Data
public class DateSingleDimValueDTO {

    /**表示时间，可以是日期、时间等，会智能排序，必须，扩展：数值也是支持的*/
    private String date;

    /**维度值，必须*/
    private String dim;

    /**数值，必须*/
    private BigDecimal value;

    /**
     * 找出所有的dim，保持按提供的数据的顺序
     */
    public static List<String> getDims(List<DateSingleDimValueDTO> data) {
        if (data == null) {
            return new ArrayList<>();
        }
        List<String> dimList = new ArrayList<>();
        Set<String> dimSet = new HashSet<>();
        for (DateSingleDimValueDTO d : data) {
            if (!dimSet.contains(d.getDim())) {
                dimList.add(d.getDim());
                dimSet.add(d.getDim());
            }
        }
        return dimList;
    }

    public static List<String> getDatesSortAsc(List<DateSingleDimValueDTO> data) {
        Set<String> datesSet = ListUtils.toSet(data, DateSingleDimValueDTO::getDate);
        List<String> datesList = ListUtils.toList(datesSet);
        ChartCommonUtils.smartSortByDate(datesList);
        return datesList;
    }

    /**
     * 判断时间是否为数值，如果是数值，则返回true
     */
    public static boolean isDatesAsNumber(List<DateSingleDimValueDTO> data) {
        String regex = "^[+-]?(\\d+(\\.\\d+)?|\\.\\d+)$";
        for (DateSingleDimValueDTO d : data) {
            if (d.getDate() != null && !d.getDate().matches(regex)) {
                return false;
            }
        }
        return true;
    }

}
