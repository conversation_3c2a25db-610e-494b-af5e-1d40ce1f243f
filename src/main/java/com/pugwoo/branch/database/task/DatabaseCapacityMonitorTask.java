package com.pugwoo.branch.database.task;

import com.pugwoo.admin.service.AdminNotifyService;
import com.pugwoo.branch.database.entity.DatabaseCapacityLogDO;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.enums.DatabaseTypeEnum;
import com.pugwoo.branch.database.model.TableSizeInfoDTO;
import com.pugwoo.branch.database.service.DatabaseService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 负责数据库的容量监控
 */
@Component
@Slf4j
public class DatabaseCapacityMonitorTask {

    @Autowired
    private DatabaseService databaseService;
    @Autowired
    private AdminNotifyService notifyService;
    @Autowired
    private DBHelper dbHelper;

    @Synchronized(throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 0 9 * * *")
    public void check() {
        List<DatabaseDO> capacityMonitorDatabase = databaseService.getCapacityMonitorDatabase();
        Exception ex = null;
        for (DatabaseDO databaseDO : capacityMonitorDatabase) {
            try {
                monitor(databaseDO);
            } catch (Exception e) {
                log.error("database capacity monitor error", e);
                if (ex == null) {
                    ex = e;
                }
            }
        }
        if (ex != null) {
            throw new RuntimeException(ex); // 系统问题，交给定时任务异常处理器去告警
        }
    }

    private void monitor(DatabaseDO databaseDO) {
        if (!Objects.equals(databaseDO.getType(), DatabaseTypeEnum.MYSQL.getCode())) {
            log.error("only support mysql now, databaseId:{}", databaseDO.getId());
            return;
        }

        Integer capacityGb = databaseDO.getCapacityGb();
        if (capacityGb == null || capacityGb <= 0) {
            log.error("databaseId:{} capacityGb:{} is invalid", databaseDO.getId(), capacityGb);
            return;
        }

        List<TableSizeInfoDTO> allTableFileSize = databaseService.getDatabaseOperateService(databaseDO).getAllTableFileSize(databaseDO);
        long bytes = NumberUtils.sum(allTableFileSize, TableSizeInfoDTO::getDiskSizeByte).longValue();
        double gb = ((double) bytes) / 1024 / 1024 / 1024;

        // 1. 记录capacity
        DatabaseCapacityLogDO capacityLogDO = new DatabaseCapacityLogDO();
        capacityLogDO.setDatabaseId(databaseDO.getId());
        capacityLogDO.setDay(LocalDate.now());
        capacityLogDO.setUsedGb(gb);
        dbHelper.insert(capacityLogDO);

        // 2. 超过90%时告警
        if (gb >= capacityGb * 0.9) {
            String content = "数据库" + databaseDO.getName() + " " + databaseDO.getHost() + ":" + databaseDO.getPort()
                    + " 剩余容量低于10%, 当前容量：" + String.format("%.3f", gb) + "GB，容量上限：" + capacityGb + "GB";
            notifyService.sendEmail(databaseDO.getMonitorSendMail(), "数据库容量监控", content);
            return;
        }

        // 3. 预计<=6天满时告警
        LocalDate three3Before = LocalDate.now().minusDays(3);
        DatabaseCapacityLogDO three3BeforeCapacityLogDO = dbHelper.getOne(DatabaseCapacityLogDO.class,
                "where database_id=? and day=?", databaseDO.getId(), three3Before);
        if (three3BeforeCapacityLogDO != null && three3BeforeCapacityLogDO.getUsedGb() != null) { // 不为空才可以预估
            double diff = gb - three3BeforeCapacityLogDO.getUsedGb();
            if (diff <= 0) {
                return;
            }
            if (capacityGb <= gb + diff * 2) { // 2个3天的diff就是6天
                String content = "数据库" + databaseDO.getName() + " " + databaseDO.getHost() + ":" + databaseDO.getPort()
                        + " 预计6天内用满，当前容量：" + String.format("%.3f", gb) + "GB，容量上限：" + capacityGb + "GB"
                        + ", 最近6天预计将使用" + (String.format("%.3f", diff * 2) + "GB");
                notifyService.sendEmail(databaseDO.getMonitorSendMail(), "数据库容量监控", content);
            }
        }
    }

}
