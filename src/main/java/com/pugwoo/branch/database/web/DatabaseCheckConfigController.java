package com.pugwoo.branch.database.web;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.branch.database.entity.DatabaseCheckConfigDO;
import com.pugwoo.branch.database.entity.DatabaseCheckItemDO;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.service.DatabaseCheckConfigService;
import com.pugwoo.branch.database.service.DatabaseService;
import com.pugwoo.branch.database.vo.DatabaseCheckConfigVO;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Controller
@RequestMapping(value = "/database_check_config")
public class DatabaseCheckConfigController {

    @Autowired
    private DatabaseService databaseService;
    @Autowired
    private DatabaseCheckConfigService databaseCheckConfigService;
    
    @GetMapping("list")
    public String list() {
        return "database/check/database_check_config";
    }

    @ResponseBody @GetMapping("get_page")
    public WebJsonBean<?> getPage(int page, int pageSize) {
        PageData<DatabaseCheckConfigVO> pageData = databaseCheckConfigService.getPage(page, pageSize);
        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }

    @ResponseBody @PostMapping("enable_disable_config")
    public WebJsonBean<?> enableDisableConfig(Long configId, Boolean enabled) {
        WebCheckUtils.assertNotNull(configId, "缺少检查ID");
        WebCheckUtils.assertNotNull(enabled, "缺少开启参数");
        DatabaseCheckConfigDO configDO = new DatabaseCheckConfigDO();
        configDO.setId(configId);
        configDO.setIsEnabled(enabled);
        databaseCheckConfigService.insertOrUpdate(configDO);
        return WebJsonBean.ok();
    }
    
    @ResponseBody @PostMapping("add_or_update")
    public WebJsonBean<?> addOrUpdate(DatabaseCheckConfigDO databaseCheckConfigDO) {
        WebCheckUtils.assertNotNull(databaseCheckConfigDO, "缺少修改的对象参数");
        WebCheckUtils.clearBaseInfo(databaseCheckConfigDO);
        WebCheckUtils.assertNotBlank(databaseCheckConfigDO.getName(), "检查名称不能为空");
        WebCheckUtils.assertNotNull(databaseCheckConfigDO.getDatabaseId(), "数据库配置id不能为空");
        if ((databaseCheckConfigDO.getRateSeconds() == null
                || databaseCheckConfigDO.getRateSeconds() <= 0)
                && StringTools.isBlank(databaseCheckConfigDO.getCronExpression())) {
            throw new AdminInnerException(AdminErrorCode.ILLEGAL_PARAMETERS, "检查频率(秒)和cron表达式不能同时为空");
        }
        WebCheckUtils.assertNotBlank(databaseCheckConfigDO.getSql(), "检查sql不能为空");
        WebCheckUtils.assertNotBlank(databaseCheckConfigDO.getAssertion(), "断言表达式不能为空");

        // 清除掉执行信息
        databaseCheckConfigDO.setIsLastSuccess(null);
        databaseCheckConfigDO.setLastTime(null);
        databaseCheckConfigDO.setLastErrorTime(null);
        databaseCheckConfigDO.setCountSuccess(null);
        databaseCheckConfigDO.setCountError(null);

        if (databaseCheckConfigDO.getRateSeconds() == null) {
            databaseCheckConfigDO.setRateSeconds(0);
        }
        
        ResultBean<Long> result = databaseCheckConfigService.insertOrUpdate(databaseCheckConfigDO);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }
    
    @ResponseBody @PostMapping("delete")
    public WebJsonBean<?> delete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(databaseCheckConfigService.deleteById(id));
    }

    @ResponseBody @PostMapping("try_check")
    public WebJsonBean<?> tryCheck(DatabaseCheckConfigDO configDO) {
        WebCheckUtils.assertNotNull(configDO, "缺少修改的对象参数");
        WebCheckUtils.assertNotNull(configDO.getDatabaseId(), "数据库配置id不能为空");
        WebCheckUtils.assertNotBlank(configDO.getSql(), "检查sql不能为空");
        WebCheckUtils.assertNotBlank(configDO.getAssertion(), "断言表达式不能为空");

        DatabaseDO databaseDO = databaseService.getById(configDO.getDatabaseId());

        DatabaseCheckItemDO checkResult = databaseCheckConfigService.executeAndAssertSql(databaseDO,
                configDO.getDatabaseName(), configDO.getSql(), configDO.getAssertion(), configDO.getDetailSql());
        return WebJsonBean.ok(MapUtils.of(
                 "isSuccess", checkResult.getIsSuccess(),
                "errorMsg", checkResult.getErrorMsg(),
                "sqlRowsJson", checkResult.getSqlRowsJson()));
    }

    @ResponseBody @PostMapping("test_send_email")
    public WebJsonBean<?> testSendEmail(String sendEmail) {
        WebCheckUtils.assertNotBlank(sendEmail, "缺少参数sendEmail");
        databaseCheckConfigService.sendEmail(sendEmail);
        return WebJsonBean.ok();
    }

}
