package com.pugwoo.branch.database.model;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 和表大小、行数相关的信息
 */
@Data
public class TableSizeInfoDTO {

    /**
     * 数据库名称
     */
    @Column("database")
    private String database;

    /**
     * 表名称
     */
    @Column("table")
    private String table;

    /**
     * 表引擎
     */
    @Column("engine")
    private String engine;

    /**
     * 真实占用磁盘大小，字节形式表示大小
     */
    @Column("disk_size_byte")
    private Long diskSizeByte;

    /**
     * 逻辑上数据大小，字节形式表示大小
     */
    @Column("logic_size_byte")
    private Long logicSizeByte;

    /**
     * 行数
     */
    @Column("rows")
    private Long rows;

    /**
     * 压缩比，百分比
     */
    public BigDecimal getCompressPercent() {
        if (diskSizeByte == null || diskSizeByte == 0 || logicSizeByte == null || logicSizeByte == 0) {
            return BigDecimal.ZERO;
        }
        return NumberUtils.percent(BigDecimal.valueOf(diskSizeByte),
                BigDecimal.valueOf(logicSizeByte), 2);
    }
}
