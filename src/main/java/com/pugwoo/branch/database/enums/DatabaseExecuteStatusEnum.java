package com.pugwoo.branch.database.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum DatabaseExecuteStatusEnum {

    WAIT("WAIT", "待执行"),

    RUNNING("RUNNING", "执行中"),

    FAILED("FAILED", "失败"),

    SUCCESS("SUCCESS", "成功"),

    STOPPED("STOPPED", "人工停止"),

    ;

    final private String code;
    final private String name;

    DatabaseExecuteStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DatabaseExecuteStatusEnum getByCode(String code) {
        for (DatabaseExecuteStatusEnum e : DatabaseExecuteStatusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        DatabaseExecuteStatusEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}