package com.pugwoo.branch.database.entity;// package a.b.c;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 检查sql明细表
 */
@Data
@ToString
@Table("database_check_item")
public class DatabaseCheckItemDO extends AdminBaseDO {

    /** 检查sql配置表id<br/>Column: [database_check_config_id] */
    @Column(value = "database_check_config_id")
    private Long databaseCheckConfigId;

    /** 本次检查sql<br/>Column: [sql] */
    @Column(value = "sql")
    private String sql;

    /** 本次检查断言表达式<br/>Column: [assertion] */
    @Column(value = "assertion")
    private String assertion;

    /** sql执行时间 毫秒<br/>Column: [sql_time_ms] */
    @Column(value = "sql_time_ms")
    private Integer sqlTimeMs;

    /** sql执行返回值<br/>Column: [sql_rows_json] */
    @Column(value = "sql_rows_json")
    private String sqlRowsJson;

    /** 本次检查明细sql<br/>Column: [detail_sql] */
    @Column(value = "detail_sql")
    private String detailSql;

    /** 明细执行的结果<br/>Column: [detail_rows_json] */
    @Column(value = "detail_rows_json")
    private String detailRowsJson;

    /** 执行是否成功 1成功 0失败<br/>Column: [is_success] */
    @Column(value = "is_success")
    private Boolean isSuccess;
    
    /** 错误信息<br/>Column: [error_msg] */
    @Column(value = "error_msg", insertValueScript = "''")
    private String errorMsg;
}
