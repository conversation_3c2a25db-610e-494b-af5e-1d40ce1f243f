package com.pugwoo.branch.database.entity;// package a.b.c;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 检查sql配置表
 */
@Data
@ToString
@Table("database_check_config")
public class DatabaseCheckConfigDO extends AdminBaseDO {

    /** 检查名称<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 数据库连接id<br/>Column: [database_id] */
    @Column(value = "database_id")
    private Long databaseId;

    /** 表名,允许为空<br/>Column: [database_name] */
    @Column(value = "database_name")
    private String databaseName;

    /** 是否是内置的检查<br/>Column: [is_build_in] */
    @Column(value = "is_build_in")
    private Boolean isBuildIn;

    /** 内置检查的代号<br/>Column: [build_in_code] */
    @Column(value = "build_in_code")
    private String buildInCode;

    /** 分类 只是一个标识<br/>Column: [category] */
    @Column(value = "category")
    private String category;

    /** 检查sql，多个分号隔开<br/>Column: [sql] */
    @Column(value = "sql")
    private String sql;

    /** 检查断言表达式<br/>Column: [assertion] */
    @Column(value = "assertion")
    private String assertion;

    /** 检查频率<br/>Column: [rate_seconds] */
    @Column(value = "rate_seconds")
    private Integer rateSeconds;

    /** 定时任务表达式<br/>Column: [cron_expression] */
    @Column(value = "cron_expression")
    private String cronExpression;

    /** 当成功时，写入的频率<br/>Column: [succ_log_rate_secs] */
    @Column(value = "succ_log_rate_secs")
    private Integer succLogRateSecs;

    /** 当检查出异常时，此sql可以记录下当时的情况；，多个分号隔开<br/>Column: [detail_sql] */
    @Column(value = "detail_sql")
    private String detailSql;

    /** 启用禁止 1启用 0禁止<br/>Column: [enabled] */
    @Column(value = "is_enabled")
    private Boolean isEnabled;

    /** 备注<br/>Column: [comment] */
    @Column(value = "comment")
    private String comment;

    /** 最后一次执行结果 1成功 0错误<br/>Column: [last_success] */
    @Column(value = "is_last_success")
    private Boolean isLastSuccess;

    /** 最后一次执行时间<br/>Column: [last_time] */
    @Column(value = "last_time")
    private Date lastTime;

    /** 最后一次错误时间<br/>Column: [last_error_time] */
    @Column(value = "last_error_time")
    private Date lastErrorTime;

    /** 正常次数<br/>Column: [count_success] */
    @Column(value = "count_success")
    private Integer countSuccess;

    /** 异常次数<br/>Column: [count_error] */
    @Column(value = "count_error")
    private Integer countError;

    /** 错误时发送邮件<br/>Column: [send_email] */
    @Column(value = "send_email")
    private String sendEmail;

}
