package com.pugwoo.branch.database.service.impl;

import com.pugwoo.branch.database.entity.DatabaseCheckItemDO;
import com.pugwoo.branch.database.service.DatabaseCheckItemService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.sql.WhereSQL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DatabaseCheckItemServiceImpl implements DatabaseCheckItemService {

    @Autowired
    private DBHelper dbHelper;

    @Override
    public PageData<DatabaseCheckItemDO> getPage(int page, int pageSize, Boolean isSuccess, Long databaseCheckConfigId) {
        WhereSQL where = new WhereSQL();
        if (isSuccess != null) {
            where.and("is_success=?", isSuccess);
        }
        if (databaseCheckConfigId != null) {
            where.and("database_check_config_id=?", databaseCheckConfigId);
        }

        where.addOrderBy("id desc");
        return dbHelper.getPage(DatabaseCheckItemDO.class, page, pageSize,
                where.getSQL(), where.getParams());
    }

    @Override
    public void deleteFailRecord(Long databaseCheckConfigId) {
        if (databaseCheckConfigId == null) {
            return;
        }
        dbHelper.delete(DatabaseCheckItemDO.class, "where database_check_config_id=? and is_success=0",
                databaseCheckConfigId);
    }
}
