package com.pugwoo.branch.database.service.oper_impl;

import com.clickhouse.client.config.ClickHouseDefaults;
import com.clickhouse.jdbc.ClickHouseDataSource;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.enums.DatabaseTypeEnum;
import com.pugwoo.branch.database.model.*;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * clickhouse的操作实现
 */
@Service
public class ClickhouseOperateServiceImpl extends BaseJdbcOperateService {

    @Override
    public DatabaseTypeEnum getSupportedDatabaseType() {
        return DatabaseTypeEnum.CLICKHOUSE;
    }

    @Override
    public String getEscapeChar() {
        return "`";
    }

    @Override
    protected DataSource getDataSourceForImplement(DatabaseDO databaseDO) {
        String url = String.format("jdbc:ch://%s:%d?compress=0", databaseDO.getHost(), databaseDO.getPort());
        Properties properties = new Properties();
        properties.setProperty(ClickHouseDefaults.USER.getKey(), databaseDO.getUsername());
        properties.setProperty(ClickHouseDefaults.PASSWORD.getKey(), databaseDO.getPassword());
        try {
            return new ClickHouseDataSource(url, properties);
        } catch (SQLException e) {
            throw new RuntimeException("ClickHouseDataSource实例化失败", e);
        }
    }

    @Override
    public Integer getClusterNodeCount(DatabaseDO databaseDO) {
        return executeQuery(databaseDO, dbHelper -> dbHelper.getRawOne(Integer.class,
                "select count(distinct host_address) from system.clusters"));
    }

    @SuppressWarnings("rawtypes")
    @Override
    public Map<String, Integer> getShardNum(DatabaseDO databaseDO) {
        List<Map> list = executeQuery(databaseDO, dbHelper -> dbHelper.getRaw(Map.class,
                "select cluster,count(distinct shard_num) as shard_num from system.clusters group by cluster"));
        Map<String, Integer> result = new HashMap<>();
        for (Map m : list) {
            result.put(m.get("cluster").toString(), NumberUtils.parseInt(m.get("shard_num")));
        }
        return result;
    }

    @Override
    public List<TableSizeInfoDTO> getAllTableFileSize(DatabaseDO databaseDO) {
        return executeQuery(databaseDO, dbHelper -> dbHelper.getRaw(TableSizeInfoDTO.class,
                """
                        SELECT database,
                               table,
                               any(engine) as engine,
                               -- sum(data_compressed_bytes) as disk_size_byte, -- 不用这个了
                               sum(data_uncompressed_bytes) as logic_size_byte,
                               sum(bytes_on_disk)           as disk_size_byte,
                               sum(rows)                    as rows
                        FROM system.parts
                        group by database, table
                        union all -- 加上外部的remote表
                        SELECT database, name as table, engine,
                               0 as logic_size_byte, 0 as disk_size_byte, 0 as rows
                        FROM system.tables
                        WHERE (engine like '%MySQL%' or engine like '%PostgreSQL%')
                        """
        ));
    }

    protected List<ColumnInfoDTO> listColumnInfo(JdbcTemplate jdbcTemplate, String databaseName, String tableName) {
        if (StringTools.isNotBlank(databaseName)) {
            jdbcTemplate.execute("use " + databaseName);
        }
        List<Map<String, Object>> list = jdbcTemplate.queryForList("desc " + tableName);
        return ListUtils.transform(list, o -> {
            ColumnInfoDTO info = new ColumnInfoDTO();
            info.setColumnName(o.getOrDefault("name", "").toString());
            info.setColumnComment(o.getOrDefault("comment", "").toString());
            return info;
        });
    }

    @Override
    public ProcessListRespDTO queryProcessList(DatabaseDO databaseDO, ProcessListReqDTO config) {
        return new ProcessListRespDTO(); // TODO 待实现
    }

    @Override
    public boolean killProcess(DatabaseDO databaseDO, Long threadId) {
        return false; // TODO 待实现
    }

    @Override
    public Map<String, String> queryDBVariables(DatabaseDO databaseDO) {
        return Map.of(); // TODO 待添加
    }

    @Override
    public List<SystemVariablesDTO> querySystemVariables(DatabaseDO databaseDO) {
        return List.of(); // TODO 待实现
    }
}
