package com.pugwoo.branch.database.service.oper_impl;

import com.mysql.cj.conf.PropertyDefinitions;
import com.mysql.cj.jdbc.MysqlDataSource;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.entity.DatabaseVariablesDO;
import com.pugwoo.branch.database.enums.DatabaseTypeEnum;
import com.pugwoo.branch.database.model.*;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.RegexUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> <br>
 * 2022/08/20 <br>
 *
 * MySQL数据库的操作实现
 */
@Slf4j
@Service
public class MysqlOperateServiceImpl extends BaseJdbcOperateService {

    @Autowired
    private DBHelper dbHelper;
    
    @Override
    public DatabaseTypeEnum getSupportedDatabaseType() {
        return DatabaseTypeEnum.MYSQL;
    }

    @Override
    public String getEscapeChar() {
        return "`";
    }

    @SneakyThrows
    @Override
    protected DataSource getDataSourceForImplement(DatabaseDO databaseDO) {
        MysqlDataSource dataSource = new MysqlDataSource();
        dataSource.setServerName(databaseDO.getHost());
        dataSource.setPort(databaseDO.getPort());
        dataSource.setUser(databaseDO.getUsername());
        dataSource.setPassword(databaseDO.getPassword());
        dataSource.setZeroDateTimeBehavior(PropertyDefinitions.ZeroDatetimeBehavior.CONVERT_TO_NULL.toString()); // 空日期转换成null
        dataSource.setAllowPublicKeyRetrieval(true); // 兼容mysql 8.0.39以上版本加的限制
        return dataSource;
    }

    @Override
    public Integer getClusterNodeCount(DatabaseDO databaseDO) {
        return 1; // MySQL不是真正的分布式，一般是主从，主节点和从节点都是完全一样的数据，而不是分布式节点来分片存储数据
    }

    @Override
    public Map<String, Integer> getShardNum(DatabaseDO databaseDO) {
        return null; // MySQL不是真正的分布式，一般是主从，主节点和从节点都是完全一样的数据，而不是分布式节点来分片存储数据
    }

    @Override
    public List<TableSizeInfoDTO> getAllTableFileSize(DatabaseDO databaseDO) {
        return executeQuery(databaseDO, dbHelper -> dbHelper.getRaw(TableSizeInfoDTO.class,
                """
                        SELECT
                          table_schema AS `database`,
                          TABLE_NAME AS `table`,
                          `ENGINE` AS `engine`,
                          table_rows * AVG_ROW_LENGTH AS `logic_size_byte`,
                          data_length + index_length + data_free AS `disk_size_byte`,
                          table_rows AS `rows`
                        FROM
                          information_schema.TABLES
                        WHERE table_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')
                          AND TABLE_TYPE = 'BASE TABLE'
                        """
        ));
    }

    protected List<ColumnInfoDTO> listColumnInfo(JdbcTemplate jdbcTemplate, String databaseName, String tableName) {
        if (StringTools.isNotBlank(databaseName)) {
            jdbcTemplate.execute("use " + databaseName);
        }
        List<Map<String, Object>> list = jdbcTemplate.queryForList("SHOW FULL COLUMNS FROM " + tableName);
        return ListUtils.transform(list, o -> {
            ColumnInfoDTO info = new ColumnInfoDTO();
            info.setColumnName(o.getOrDefault("Field", "").toString());
            info.setColumnComment(o.getOrDefault("Comment", "").toString());
            return info;
        });
    }

    @Override
    public ProcessListRespDTO queryProcessList(DatabaseDO databaseDO, ProcessListReqDTO config) {
        boolean isQueryRowLock = config != null && config.getIsQueryRowLock() != null && config.getIsQueryRowLock();

        Set<String> writeLockType = new HashSet<>(); // 锁类型中属于写锁的类型
        writeLockType.add("INTENTION_EXCLUSIVE");
        writeLockType.add("SHARED_WRITE");
        writeLockType.add("SHARED_NO_READ_WRITE");
        writeLockType.add("EXCLUSIVE");

        ProcessListRespDTO resp = new ProcessListRespDTO();

        // 1. 查询processlist，查询INFORMATION_SCHEMA.PROCESSLIST的Info最长是65535，这里先用16k，这里的Id是processId
        List<Map> list = executeQuery(databaseDO, dbHelper -> dbHelper.getRaw(Map.class,
                """
                        SELECT Id,User,Host,db,Command,Time,State,SUBSTRING(Info, 1, 16384) AS Info
                        FROM INFORMATION_SCHEMA.PROCESSLIST
                        """
        ));

        List<ProcessListDTO> result = ListUtils.transform(list, o -> {
                ProcessListDTO info = new ProcessListDTO();
                info.setId(NumberUtils.parseLong(getAsString(o, "Id")));
                info.setUser(getAsString(o, "User"));
                info.setClientIp(getAsString(o, "Host"));
                info.setDb(getAsString(o, "db"));
                info.setCommand(getAsString(o, "Command"));
                info.setSql(getAsString(o, "Info"));
                info.setTime(NumberUtils.parseInt(getAsString(o, "Time")));
                info.setState(getAsString(o, "State"));
                return info;
            });
        Map<Long, ProcessListDTO> processMap = ListUtils.toMap(result, o -> o.getId(), o -> o);

        long start = System.currentTimeMillis();

        // 2. 再查询锁信息，performance_schema.metadata_locks
        List<Map> metaLocks = executeQuery(databaseDO, dbHelper -> dbHelper.getRaw(Map.class,
                    "select * from performance_schema.metadata_locks"));

        // 3. 再查询 performance_schema.data_locks 行锁，这里加limit也没有用，照样占内存
        List<Map> recordLocks = new ArrayList<>();
        List<Map> recordWaitLocks = new ArrayList<>();
        Map<Long, LocksAndUndoDTO> innoStatusRecordLocks = queryLocksAndUndoFromInnoStatus(databaseDO);

        if (isQueryRowLock) {
            // 这个是查询每一行行锁的明细，生产禁用
            recordLocks = queryIgnoreTableNotExist(databaseDO, "select * from performance_schema.data_locks");
        } else {
            recordWaitLocks = queryIgnoreTableNotExist(databaseDO, "select * from sys.innodb_lock_waits");
        }

        long cost = System.currentTimeMillis() - start;
        resp.setQueryLockCostMs(cost);

        // 4. 查询threadId到proccessId的映射关系
        Map<Long, Long> threadIdToProcessIdMap = getThreadIdToProcessIdMap(databaseDO,
                ListUtils.transform(metaLocks, o -> NumberUtils.parseLong(o.get("OWNER_THREAD_ID"))),
                ListUtils.transform(recordLocks, o -> NumberUtils.parseLong(o.get("THREAD_ID"))),
                ListUtils.transform(recordWaitLocks, o -> NumberUtils.parseLong(o.get("waiting_pid"))),
                ListUtils.transform(recordWaitLocks, o -> NumberUtils.parseLong(o.get("blocking_pid"))));

        // 5. 处理metalocks
        metaLocks.forEach(o -> {
            Long threadId = NumberUtils.parseLong(o.get("OWNER_THREAD_ID"));
            ProcessListDTO processListDTO = getProcessListDTOByThreadId(threadId, threadIdToProcessIdMap, processMap);
            if (processListDTO != null) { // 由于时间差关系，也是可能为null的
                ProcessListLockDTO lock = new ProcessListLockDTO();
                lock.setScopeType("METADATA_LOCK");
                lock.setLockType(getAsString(o, "LOCK_TYPE"));
                lock.setIsWriteLock(writeLockType.contains(lock.getLockType()));
                lock.setLockDuration(getAsString(o, "LOCK_DURATION"));
                lock.setLockStatus(getAsString(o, "LOCK_STATUS"));
                lock.setObjectType(getAsString(o, "OBJECT_TYPE"));
                lock.setDatabase(getAsString(o, "OBJECT_SCHEMA"));
                lock.setTable(getAsString(o, "OBJECT_NAME"));
                lock.setColumn(getAsString(o, "COLUMN_NAME"));
                processListDTO.getLocks().add(lock);
            }
        });

        // 统计锁的数量
        result.forEach(o -> {
            o.setHoldReadLocks(ListUtils.filter(o.getLocks(), i ->
                    "METADATA_LOCK".equals(i.getScopeType()) && "GRANTED".equals(i.getLockStatus()) && !writeLockType.contains(i.getLockType())).size());
            o.setHoldWriteLocks(ListUtils.filter(o.getLocks(), i ->
                    "METADATA_LOCK".equals(i.getScopeType()) && "GRANTED".equals(i.getLockStatus()) && writeLockType.contains(i.getLockType())).size());
            o.setWaitReadLocks(ListUtils.filter(o.getLocks(), i ->
                    "METADATA_LOCK".equals(i.getScopeType()) && "PENDING".equals(i.getLockStatus()) && !writeLockType.contains(i.getLockType())).size());
            o.setWaitWriteLocks(ListUtils.filter(o.getLocks(), i ->
                    "METADATA_LOCK".equals(i.getScopeType()) && "PENDING".equals(i.getLockStatus()) && writeLockType.contains(i.getLockType())).size());
        });


        // 6. 处理record locks
        recordLocks.forEach(o -> {
            Long threadId = NumberUtils.parseLong(o.get("THREAD_ID"));
            ProcessListDTO processListDTO = getProcessListDTOByThreadId(threadId, threadIdToProcessIdMap, processMap);
            if (processListDTO != null) { // 由于时间差关系，也是可能为null的
                ProcessListLockDTO lock = new ProcessListLockDTO();
                processListDTO.getLocks().add(lock);
                lock.setScopeType("ROW_LOCK/" + o.get("LOCK_TYPE"));
                lock.setLockType(getAsString(o, "LOCK_MODE"));
                lock.setIsWriteLock(writeLockType.contains(lock.getLockType()));
                lock.setLockDuration("");
                lock.setLockStatus(getAsString(o, "LOCK_STATUS"));
                lock.setObjectType("");
                lock.setDatabase(getAsString(o, "OBJECT_SCHEMA"));
                lock.setTable(getAsString(o, "OBJECT_NAME"));
                lock.setColumn("");
                lock.setRow(getAsString(o, "INDEX_NAME") + "=" + getAsString(o, "LOCK_DATA"));
            }
        });
        recordWaitLocks.forEach(o -> {
            // 先处理等待部分的锁
            {
                Long pid = NumberUtils.parseLong(o.get("waiting_pid"));
                ProcessListDTO processListDTO = processMap.get(pid);
                if (processListDTO != null) { // 由于时间差关系，也是可能为null的
                    ProcessListLockDTO lock = new ProcessListLockDTO();
                    if (processListDTO.getLocks() == null) {
                        processListDTO.setLocks(new ArrayList<>());
                    }
                    processListDTO.getLocks().add(lock);
                    lock.setScopeType("ROW_LOCK/" + o.get("locked_type"));
                    lock.setLockType(getAsString(o, "waiting_lock_mode"));
                    lock.setIsWriteLock(writeLockType.contains(lock.getLockType()));
                    lock.setLockDuration("");
                    lock.setLockStatus("WAITING");
                    lock.setObjectType("");
                    lock.setDatabase(getAsString(o, "locked_table_schema"));
                    lock.setTable(getAsString(o, "locked_table_name"));
                    lock.setColumn("");
                    lock.setLockedIndex(getAsString(o, "locked_index"));
                    lock.setWaitingSeconds(NumberUtils.parseInt(o.get("wait_age_secs")));
                    lock.setRowsLocked(NumberUtils.parseInt(o.get("waiting_trx_rows_locked")));
                    lock.setWaitProcessId(NumberUtils.parseLong(o.get("blocking_pid")));
                }
            }

            // 被等待的其实就是加锁的了
            {
                Long pid = NumberUtils.parseLong(o.get("blocking_pid"));
                ProcessListDTO processListDTO = processMap.get(pid);
                if (processListDTO != null) { // 由于时间差关系，也是可能为null的
                    ProcessListLockDTO lock = new ProcessListLockDTO();
                    if (processListDTO.getLocks() == null) {
                        processListDTO.setLocks(new ArrayList<>());
                    }
                    processListDTO.getLocks().add(lock);
                    lock.setScopeType("ROW_LOCK/" + o.get("locked_type"));
                    lock.setLockType(getAsString(o, "blocking_lock_mode"));
                    lock.setIsWriteLock(writeLockType.contains(lock.getLockType()));
                    lock.setLockDuration("");
                    lock.setLockStatus("GRANTED");
                    lock.setObjectType("");
                    lock.setDatabase(getAsString(o, "locked_table_schema"));
                    lock.setTable(getAsString(o, "locked_table_name"));
                    lock.setColumn("");
                    lock.setLockedIndex(getAsString(o, "locked_index"));
                    lock.setRowsLocked(NumberUtils.parseInt(o.get("blocking_trx_rows_locked")));
                }
            }
        });

        result.forEach(o -> {
            LocksAndUndoDTO locksAndUndoDTO = innoStatusRecordLocks.get(o.getId());
            if (isQueryRowLock) {
                o.setHoldRowLocks(ListUtils.filter(o.getLocks(), i ->
                        i.getScopeType().equals("ROW_LOCK/RECORD") && "GRANTED".equals(i.getLockStatus())).size());
            } else {
                o.setHoldRowLocks(locksAndUndoDTO == null ? 0 : locksAndUndoDTO.getRowLocks());
            }
            o.setUndoEntries(locksAndUndoDTO == null ? 0 : locksAndUndoDTO.getUndoEntries());
            o.setWaitRowLocks(ListUtils.filter(o.getLocks(), i ->
                    i.getScopeType().equals("ROW_LOCK/RECORD") && "WAITING".equals(i.getLockStatus())).size());
        });

        resp.setProcessList(result);

        return resp;
    }

    private ProcessListDTO getProcessListDTOByThreadId(Long threadId, Map<Long, Long> threadIdToProcessIdMap,
                                                       Map<Long, ProcessListDTO> processMap) {
        Long processId = threadIdToProcessIdMap.get(threadId);
        if (processId != null) { // 由于时间差的关系，这里是可能线程已经结束了，找不到到对应的processId，找不到就不处理了
            ProcessListDTO processListDTO = processMap.get(processId);
            if (processListDTO != null && processListDTO.getLocks() == null) {
                processListDTO.setLocks(new ArrayList<>());
            }
            return processListDTO;
        } else {
            return null;
        }
    }

    @Data
    public static class LocksAndUndoDTO {
        private Integer rowLocks;
        private Integer undoEntries;
    }

    /**
     * 从show engine innodb status
     * @return processId -> LocksAndUndoDTO
     */
    private Map<Long, LocksAndUndoDTO> queryLocksAndUndoFromInnoStatus(DatabaseDO databaseDO) {
        List<Map> innoStatus = executeQuery(databaseDO, dbHelper -> dbHelper.getRaw(Map.class,
                """
                        show engine innodb status
                        """));
        if (ListUtils.isEmpty(innoStatus) || innoStatus.getFirst().get("Status") == null) {
            return new HashMap<>();
        }

        String status = innoStatus.getFirst().get("Status").toString();
        String[] strings = StringTools.splitLines(status);
        Map<Long, LocksAndUndoDTO> result = new HashMap<>();
        for (int i = 0; i <strings.length; i++) {
            if (strings[i].startsWith("---TRANSACTION ") && strings[i].contains("ACTIVE")) {
                Long processId = null;
                int rowLocks = 0;
                int undoLogEntries = 0;
                for (int j = 1; i + j < strings.length; j++) {
                    if (strings[i + j].startsWith("---")) {
                        break;
                    }
                    Long tmpProcessId = NumberUtils.parseLong(RegexUtils.getFirstMatchStr(strings[i + j], "thread id (\\d+)"));
                    if (tmpProcessId != null) {
                        processId = tmpProcessId;
                    }
                    Integer tmpRowLocks = NumberUtils.parseInt(RegexUtils.getFirstMatchStr(strings[i + j], "(\\d+) row lock"));
                    if (tmpRowLocks != null) {
                        rowLocks = tmpRowLocks;
                    }
                    Integer tmpUndoLogEntries = NumberUtils.parseInt(RegexUtils.getFirstMatchStr(strings[i + j], "undo log entries (\\d+)"));
                    if (tmpUndoLogEntries != null) {
                        undoLogEntries = tmpUndoLogEntries;
                    }
                }
                if (processId != null) {
                    LocksAndUndoDTO dto = new LocksAndUndoDTO();
                    dto.setRowLocks(rowLocks);
                    dto.setUndoEntries(undoLogEntries);
                    result.put(processId, dto);
                }
            }
        }

        return result;
    }

    private Map<Long, Long> getThreadIdToProcessIdMap(DatabaseDO databaseDO, List<Long> metaLocksThreadId,
                                                      List<Long> recordLocksThreadId,
                                                      List<Long> recordWaitLocksThreadId1,
                                                      List<Long> recordWaitLocksThreadId2) {
        Map<Long, Long> threadIdToProcessIdMap = new HashMap<>();
        Set<Long> threadIds = new HashSet<>();
        if (metaLocksThreadId != null) {
            threadIds.addAll(metaLocksThreadId);
        }
        if (recordLocksThreadId != null) {
            threadIds.addAll(recordLocksThreadId);
        }
        if (recordWaitLocksThreadId1 != null) {
            threadIds.addAll(recordWaitLocksThreadId1);
        }
        if (recordWaitLocksThreadId2 != null) {
            threadIds.addAll(recordWaitLocksThreadId2);
        }

        if (!threadIds.isEmpty()) {
            List<Map> threadIdToProcessListId = executeQuery(databaseDO, dbHelper -> dbHelper.getRaw(Map.class,
                    """
                            SELECT THREAD_ID,PROCESSLIST_ID FROM performance_schema.threads where THREAD_ID in (?)
                            """, threadIds));
            threadIdToProcessListId.forEach(o -> {
                Long threadId = NumberUtils.parseLong(o.get("THREAD_ID"));
                Long processlistId = NumberUtils.parseLong(o.get("PROCESSLIST_ID"));
                if (threadId != null && processlistId != null) {
                    threadIdToProcessIdMap.put(threadId, processlistId);
                }
            });
        }
        return threadIdToProcessIdMap;
    }

    private static String getAsString(Map map, String key) {
        Object value = map.get(key);
        return value == null ? "" : value.toString();
    }

    @Override
    public boolean killProcess(DatabaseDO databaseDO, Long threadId) {
        if (threadId == null) {
            return false;
        }
        try {
            executeQuery(databaseDO, dbHelper -> dbHelper.executeRaw("kill " + threadId));
            return true;
        } catch (Exception e) {
            log.error("kill thread id {} fail, database id:{}", threadId, databaseDO.getId(), e);
            return false;
        }
    }

    @Override
    public Map<String, String> queryDBVariables(DatabaseDO databaseDO) {
        List<Map> list = executeQuery(databaseDO, dbHelper -> dbHelper.getRaw(Map.class,
                """
                        SHOW VARIABLES LIKE 'performance_schema'
                        """));
        Map<String, String> result = new HashMap<>();
        for (Map map : list) {
            String variableName = map.getOrDefault("Variable_name", "").toString();
            result.put(variableName, map.getOrDefault("Value", "").toString());
        }
        return result;
    }

    @Override
    public List<SystemVariablesDTO> querySystemVariables(DatabaseDO databaseDO) {
        List<DatabaseVariablesDO> all = dbHelper.getAll(DatabaseVariablesDO.class,
                "where database_type=?", DatabaseTypeEnum.MYSQL.getCode());
        List<SystemVariablesDTO> result = new ArrayList<>();
        for (DatabaseVariablesDO databaseVariablesDO : all) {
            String sql = databaseVariablesDO.getSql();
            if (StringTools.isBlank(sql)) {
                continue;
            }
            String value = executeQuery(databaseDO, dbHelper -> dbHelper.getRawOne(String.class, sql));
            result.add(new SystemVariablesDTO(databaseVariablesDO, value));
        }

        return result;
    }

    private List<Map> queryIgnoreTableNotExist(DatabaseDO databaseDO, String sql) {
        try {
            return executeQuery(databaseDO, dbHelper -> dbHelper.getRaw(Map.class, sql));
        } catch (Exception e) {
            // ignore table not exist
            if (e.getCause() != null && e.getCause().getMessage().contains("exist")) {
                // ignore
                return new ArrayList<>();
            } else {
                throw new RuntimeException(e);
            }
        }
    }
}
