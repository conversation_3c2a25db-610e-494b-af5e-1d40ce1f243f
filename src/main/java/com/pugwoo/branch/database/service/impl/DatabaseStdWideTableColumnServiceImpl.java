package com.pugwoo.branch.database.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.database.entity.DatabaseStdWideTableColumnDO;
import com.pugwoo.branch.database.entity.DatabaseStdWideTableDO;
import com.pugwoo.branch.database.model.ColumnInfoDTO;
import com.pugwoo.branch.database.service.DatabaseOperateService;
import com.pugwoo.branch.database.service.DatabaseService;
import com.pugwoo.branch.database.service.DatabaseStdWideTableColumnService;
import com.pugwoo.branch.database.service.DatabaseStdWideTableService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class DatabaseStdWideTableColumnServiceImpl implements DatabaseStdWideTableColumnService {

    @Autowired
    private DBHelper dbHelper;

    @Autowired
    private DatabaseStdWideTableService databaseStdWideTableService;

    @Autowired
    private DatabaseService databaseService;

    @Override
    public DatabaseStdWideTableColumnDO getById(Long id) {
        if(id == null) {
           return null;
        }
        return dbHelper.getByKey(DatabaseStdWideTableColumnDO.class, id);
    }

    @Override
    public PageData<DatabaseStdWideTableColumnDO> getPage(int page, int pageSize) {
        return dbHelper.getPage(DatabaseStdWideTableColumnDO.class, page, pageSize);
    }

    @Override
    public PageData<DatabaseStdWideTableColumnDO> getPage(int page, int pageSize, Long tableId) {
        if (tableId == null) {
            return getPage(page, pageSize);
        }
        return dbHelper.getPage(DatabaseStdWideTableColumnDO.class, page, pageSize,
            "where table_id = ? order by seq asc, id asc", tableId);
    }

    @Override
    public ResultBean<Long> insertOrUpdate(DatabaseStdWideTableColumnDO databaseStdWideTableColumnDO) {
        if(databaseStdWideTableColumnDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        int rows = dbHelper.insertOrUpdate(databaseStdWideTableColumnDO);
        return rows > 0 ? ResultBean.ok(databaseStdWideTableColumnDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteById(Long id) {
        if(id == null) {
            return false;
        }

        DatabaseStdWideTableColumnDO databaseStdWideTableColumnDO = new DatabaseStdWideTableColumnDO();
        databaseStdWideTableColumnDO.setId(id);
        return dbHelper.delete(databaseStdWideTableColumnDO) > 0;
    }

    @Override
    public List<ColumnInfoDTO> getUnaddedColumns(Long tableId) {
        if (tableId == null) {
            return ListUtils.newArrayList();
        }

        // 1. 获取标准宽表信息
        DatabaseStdWideTableDO stdWideTable = databaseStdWideTableService.getById(tableId);
        if (stdWideTable == null) {
            return ListUtils.newArrayList();
        }

        // 2. 获取数据库连接信息
        var databaseDO = databaseService.getById(stdWideTable.getDatabaseId());
        if (databaseDO == null) {
            return ListUtils.newArrayList();
        }

        // 3. 获取数据库操作服务
        DatabaseOperateService operateService = databaseService.getDatabaseOperateService(databaseDO);

        // 4. 获取数据库表的所有列信息
        List<ColumnInfoDTO> allColumns = operateService.listColumnInfo(databaseDO, stdWideTable.getDatabaseName(), stdWideTable.getTableName());

        // 5. 获取已经添加的列名集合
        List<DatabaseStdWideTableColumnDO> existingColumns = dbHelper.getAll(DatabaseStdWideTableColumnDO.class,
            "where table_id = ?", tableId);
        Set<String> existingColumnNames = existingColumns.stream()
            .map(DatabaseStdWideTableColumnDO::getColumnName)
            .collect(Collectors.toSet());

        // 6. 过滤出未添加的列
        return allColumns.stream()
            .filter(column -> !existingColumnNames.contains(column.getColumnName()))
            .collect(Collectors.toList());
    }

    @Override
    public ResultBean<Boolean> updateColumnSort(List<Long> columnIds) {
        if (columnIds == null || columnIds.isEmpty()) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "列ID列表不能为空");
        }

        try {
            List<DatabaseStdWideTableColumnDO> toUpdate = new ArrayList<>();
            // 批量更新排序，seq从1开始
            for (int i = 0; i < columnIds.size(); i++) {
                Long columnId = columnIds.get(i);
                DatabaseStdWideTableColumnDO columnDO = new DatabaseStdWideTableColumnDO();
                columnDO.setId(columnId);
                columnDO.setSeq(i + 1); // seq从1开始
                toUpdate.add(columnDO);
            }
            dbHelper.update(toUpdate); // 批量更新，性能好
            return ResultBean.ok(true);
        } catch (Exception e) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "更新排序失败：" + e.getMessage());
        }
    }

}