package com.pugwoo.branch.database.service;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.database.entity.DatabaseStdWideTableColumnDO;
import com.pugwoo.branch.database.model.ColumnInfoDTO;
import com.pugwoo.dbhelper.model.PageData;

import java.util.List;

public interface DatabaseStdWideTableColumnService {

    /**
     * 通过主键获得数据
     */
    DatabaseStdWideTableColumnDO getById(Long id);

    /**
     * 获得分页数据
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     */
    PageData<DatabaseStdWideTableColumnDO> getPage(int page, int pageSize);

    /**
     * 获得分页数据，支持按tableId过滤
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     * @param tableId 表ID，可为null
     */
    PageData<DatabaseStdWideTableColumnDO> getPage(int page, int pageSize, Long tableId);

    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    ResultBean<Long> insertOrUpdate(DatabaseStdWideTableColumnDO databaseStdWideTableColumnDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteById(Long id);

    /**
     * 获取数据库表中还未添加到标准宽表列配置中的列信息
     * @param tableId 标准宽表ID
     * @return 未添加的列信息列表
     */
    List<ColumnInfoDTO> getUnaddedColumns(Long tableId);

    /**
     * 批量更新列的排序
     * @param columnIds 列ID数组，按新的排序顺序排列
     * @return 更新结果
     */
    ResultBean<Boolean> updateColumnSort(List<Long> columnIds);

}