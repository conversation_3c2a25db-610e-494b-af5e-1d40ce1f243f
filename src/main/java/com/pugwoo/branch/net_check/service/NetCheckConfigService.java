package com.pugwoo.branch.net_check.service;

import com.pugwoo.branch.net_check.entity.NetCheckConfigDO;
import com.pugwoo.branch.net_check.entity.NetCheckResultDO;

import java.util.List;

public interface NetCheckConfigService {

    /**
     * 获取所有启用的任务
     */
    List<NetCheckConfigDO> getAllEnabledList();

    /**
     * 执行检查并返回结果
     * 不包括：1）发送消息告警 2）将resultDO数据插入数据库
     */
    NetCheckResultDO doCheck(NetCheckConfigDO netCheckConfigDO);

    /**
     * 更新检查结果
     */
    void updateCheckResult(NetCheckResultDO netCheckResultDO, NetCheckConfigDO netCheckConfigDO);

}
