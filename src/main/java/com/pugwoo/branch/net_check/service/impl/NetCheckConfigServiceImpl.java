package com.pugwoo.branch.net_check.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.net_check.entity.NetCheckConfigDO;
import com.pugwoo.branch.net_check.entity.NetCheckResultDO;
import com.pugwoo.branch.net_check.enums.NetTypeEnum;
import com.pugwoo.branch.net_check.service.NetCheckConfigService;
import com.pugwoo.branch.net_check.service.NetCheckService;
import com.pugwoo.dbhelper.DBHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class NetCheckConfigServiceImpl implements NetCheckConfigService {

    @Autowired
    private DBHelper dbHelper;
    @Autowired
    private NetCheckService netCheckService;

    /**缓存用：用于控制成功时插入的频率，不要插入太多*/
    private final Map<Long, Date> lastInsertSuccessTime = new HashMap<>();

    @Override
    public List<NetCheckConfigDO> getAllEnabledList() {
        return dbHelper.getAll(NetCheckConfigDO.class, "where is_enabled=1");
    }

    @Override
    public NetCheckResultDO doCheck(NetCheckConfigDO netCheckConfigDO) {
        NetCheckResultDO netCheckResultDO = initFromConfig(netCheckConfigDO);

        // 路由各种测试方法
        NetTypeEnum netType = NetTypeEnum.getByCode(netCheckConfigDO.getNetType());
        try {
            switch (netType) {
                case HTTP -> netCheckService.httpCheck(netCheckConfigDO, netCheckResultDO);
                case null -> throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR,
                        "未知的netType:" + netCheckConfigDO.getNetType());
            }
        } catch (Exception e) {
            netCheckResultDO.setIsSuccess(false);
            netCheckResultDO.setErrorMsg(e.getMessage());
        }

        return netCheckResultDO;
    }

    @Override
    @Transactional
    public void updateCheckResult(NetCheckResultDO netCheckResultDO, NetCheckConfigDO netCheckConfigDO) {
        if(netCheckResultDO == null) {
            return;
        }

        boolean isSuccess = netCheckResultDO.getIsSuccess();
        if (isSuccess && netCheckConfigDO.getSuccLogRateSecs() != null) {
            boolean needInsert = true;
            Date lastTime = lastInsertSuccessTime.get(netCheckConfigDO.getId());
            if (lastTime != null && System.currentTimeMillis() - lastTime.getTime() < netCheckConfigDO.getSuccLogRateSecs() * 1000) {
                needInsert = false; // 本机插入过，可以肯定不需要插入
            } else {
                // 查询db看是否超过成功插入间隔时间（这个是为了控制成功插入的量）
                NetCheckResultDO one = dbHelper.getOne(NetCheckResultDO.class,
                        "where net_check_config_id=? order by id", netCheckConfigDO.getId());
                if (one != null && one.getIsSuccess() != null && one.getIsSuccess() && one.getCreateTime() != null
                        && System.currentTimeMillis() - one.getCreateTime().getTime() < netCheckConfigDO.getSuccLogRateSecs() * 1000) {
                    needInsert = false;
                }
            }

            if (needInsert) {
                lastInsertSuccessTime.put(netCheckConfigDO.getId(), new Date());
                dbHelper.insert(netCheckResultDO);
            }
        } else {
            if (isSuccess) {
                lastInsertSuccessTime.put(netCheckConfigDO.getId(), new Date());
            }
            dbHelper.insert(netCheckResultDO);
        }

        // 仅更新config的统计信息
        Date now = new Date();
        NetCheckConfigDO checkConfigDO = new NetCheckConfigDO();
        checkConfigDO.setId(netCheckConfigDO.getId());
        checkConfigDO.setIsLastSuccess(isSuccess);
        checkConfigDO.setLastTime(now);
        String countSetSql = "count_success = count_success + 1";
        if (!isSuccess) {
            checkConfigDO.setLastErrorTime(now);
            countSetSql = "count_error = count_error + 1";
        }
        dbHelper.update(checkConfigDO);
        dbHelper.updateCustom(checkConfigDO, countSetSql);
    }

    private static NetCheckResultDO initFromConfig(NetCheckConfigDO netCheckConfigDO) {
        NetCheckResultDO netCheckResultDO = new NetCheckResultDO();
        netCheckResultDO.setNetCheckConfigId(netCheckConfigDO.getId());
        netCheckResultDO.setNetType(netCheckConfigDO.getNetType());
        netCheckResultDO.setMethod(netCheckConfigDO.getMethod());
        netCheckResultDO.setUri(netCheckConfigDO.getUri());
        netCheckResultDO.setParams(netCheckConfigDO.getParams());
        netCheckResultDO.setTimes(netCheckConfigDO.getTimes());
        netCheckResultDO.setAssertion(netCheckConfigDO.getAssertion());

        return netCheckResultDO;
    }
}
