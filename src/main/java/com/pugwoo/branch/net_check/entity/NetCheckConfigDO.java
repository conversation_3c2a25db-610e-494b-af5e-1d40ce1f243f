package com.pugwoo.branch.net_check.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * net检查配置表
 */
@Data
@ToString
@Table("net_check_config")
public class NetCheckConfigDO extends AdminBaseDO {

    /** 检查名称<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 分类，只是一个标识<br/>Column: [category] */
    @Column(value = "category")
    private String category;

    /** 网络类型，如HTTP、TCP、PING<br/>Column: [net_type] */
    @Column(value = "net_type")
    private String netType;

    /** 请求方式，例如http的话有GET POST<br/>Column: [method] */
    @Column(value = "method")
    private String method;

    /** 请求url或唯一连接标识<br/>Column: [uri] */
    @Column(value = "uri")
    private String uri;

    /** 请求体，默认用json格式，实际情况根据net_type来<br/>Column: [params] */
    @Column(value = "params")
    private String params;

    /** 连接超时时间，单位毫秒<br/>Column: [connect_timeout_ms] */
    @Column(value = "connect_timeout_ms")
    private Integer connectTimeoutMs;

    /** 读取超时时间，单位毫秒<br/>Column: [read_timeout_ms] */
    @Column(value = "read_timeout_ms")
    private Integer readTimeoutMs;

    /** 执行次数，空值默认为1次<br/>Column: [times] */
    @Column(value = "times")
    private Integer times;

    /** 检查断言表达式，表达式入参由net_type而定<br/>Column: [assertion] */
    @Column(value = "assertion")
    private String assertion;

    /** 检查频率<br/>Column: [rate_seconds] */
    @Column(value = "rate_seconds")
    private Integer rateSeconds;

    /** 定时任务表达式<br/>Column: [cron_expression] */
    @Column(value = "cron_expression")
    private String cronExpression;

    /** 当成功时，写入的频率<br/>Column: [succ_log_rate_secs] */
    @Column(value = "succ_log_rate_secs")
    private Integer succLogRateSecs;

    /** 启用禁止 1启用 0禁止<br/>Column: [is_enabled] */
    @Column(value = "is_enabled")
    private Integer isEnabled;

    /** 备注<br/>Column: [comment] */
    @Column(value = "comment")
    private String comment;

    /** 最后一次执行结果 1成功 0错误<br/>Column: [is_last_success] */
    @Column(value = "is_last_success")
    private Boolean isLastSuccess;

    /** 最后一次执行时间<br/>Column: [last_time] */
    @Column(value = "last_time")
    private Date lastTime;

    /** 最后一次错误时间<br/>Column: [last_error_time] */
    @Column(value = "last_error_time")
    private Date lastErrorTime;

    /** 正常次数<br/>Column: [count_success] */
    @Column(value = "count_success")
    private Integer countSuccess;

    /** 异常次数<br/>Column: [count_error] */
    @Column(value = "count_error")
    private Integer countError;

    /** 分号隔开，错误时发送邮件；复用后也支持钉钉<br/>Column: [send_email] */
    @Column(value = "send_email")
    private String sendEmail;

}