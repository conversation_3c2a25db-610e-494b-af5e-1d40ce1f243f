### 创建lines多折线图，两列的情况
# configs都是可选的，不给也有默认值
POST http://127.0.0.1:8080/create_chart
Content-Type: application/json

{
  "name": "我是名称也是标题",
  "library": "echarts",
  "charts": "lines",
  "configs": {
    "colors": "green",
    "styles": "dashed",
    "smooth": false,
    "showZoom": true,
    "connectNulls": true
  },
  "titles": ["日期", "分数"],
  "data": [
    ["2024-01", 1200],
    ["2024-02", 950],
    ["2024-03", null],
    ["2024-04", null],
    ["2024-05", null],
    ["2024-06", 500],
    ["2024-07", 400]
  ]
}

### 创建lines多折线图，三列的情况
POST http://127.0.0.1:8080/create_chart
Content-Type: application/json

{
  "name": "我是名称也是标题",
  "library": "echarts",
  "charts": "lines",
  "configs": {
    "colors": {"SA2" : "green"},
    "styles": {"SA2": "dashed", "SA5": "bar"},
    "smooth": false,
    "yAxis": [{
      "name": "A类机型",
      "lines": ["SA2"]
    }, {
      "name": "B类机型",
      "lines": ["SA5"]
    }]
  },
  "titles": ["日期", "dim", ""],
  "data": [
    ["2024-01", "SA2", 1200],
    ["2024-02", "SA2",950],
    ["2024-03", "SA2",800],
    ["2024-04", "SA2",700],
    ["2024-05", "SA2",600],
    ["2024-06", "SA2",500],
    ["2024-07", "SA2",400],
    ["2024-01", "SA5", 10],
    ["2024-02", "SA5",150],
    ["2024-03", "SA5",300],
    ["2024-04", "SA5",500],
    ["2024-05", "SA5",600],
    ["2024-06", "SA5",700],
    ["2024-07", "SA5",800]
  ]
}

### x轴是数值
POST http://127.0.0.1:8080/create_chart
Content-Type: application/json

{
  "name": "我是名称也是标题",
  "library": "echarts",
  "charts": "lines",
  "configs": {
    "connectNulls": "true"
  },
  "titles": ["x轴", "分类", "大小"],
  "data": [
    [100, "国标", 1000],
    [300, "国标", 1100],
    [500, "国标", 1200],
    [1000, "国标", 1300],
    [2000, "国标", 1400],
    [5000, "国标", 1500],
    [10000, "国标", 1600],
    [100, "世标", 100],
    [300, "世标", 100],
    [500, "世标", 200],
    [1000, "世标", 300],
    [2000, "世标", 400],
    [5000, "世标", 500],
    [10000, "世标", 600]
  ]
}