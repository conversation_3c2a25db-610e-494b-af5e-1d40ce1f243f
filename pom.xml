<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.6</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>

	<groupId>com.pugwoo</groupId>
	<artifactId>branch</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>branch</name>

	<properties>
		<java.version>21</java.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.pugwoo</groupId>
			<artifactId>admin-starter</artifactId>
			<version>2.1.0</version>
		</dependency>

		<!-- git 操作工具 -->
		<dependency>
			<groupId>org.eclipse.jgit</groupId>
			<artifactId>org.eclipse.jgit</artifactId>
			<version>7.2.1.202505142326-r</version>
		</dependency>
		<!-- maven pom.xml 文件解析工具 -->
		<dependency>
			<groupId>org.apache.maven</groupId>
			<artifactId>maven-model</artifactId>
			<version>3.9.9</version>
		</dependency>

		<!-- 0.7.x 是orm能正常工作的最新版本 -->
		<dependency>
			<groupId>com.clickhouse</groupId>
			<artifactId>clickhouse-jdbc</artifactId>
			<version>0.7.2</version>
		</dependency>
		<!-- clickhosue-jdbc 0.4.6+ 默认使用lz4压缩，所以需要引入lz4-java包 -->
		<dependency>
			<groupId>org.lz4</groupId>
			<artifactId>lz4-java</artifactId>
			<version>1.8.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents.client5</groupId>
			<artifactId>httpclient5</artifactId>
			<version>5.5</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<finalName>branch</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>pugwoo-repo</id>
			<url>https://mvn-with-source-ip6ba8iccq-1251050007.cos.ap-guangzhou.myqcloud.com/</url>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</snapshots>
		</repository>
	</repositories>

</project>
